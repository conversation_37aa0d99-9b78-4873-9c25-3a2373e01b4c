/**
 * Enhanced Navbar JavaScript - Sans framework
 * Gestion des interactions de la navbar améliorée
 */

class EnhancedNavbar {
    constructor() {
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadNotifications();
        this.startNotificationPolling();
        this.handleMobileMenu();
    }
    
    bindEvents() {
        // Gestion des dropdowns
        this.handleDropdowns();
        
        // Gestion du menu mobile
        const mobileMenuBtn = document.getElementById('buttonClick');
        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', () => {
                this.toggleMobileMenu();
            });
        }
        
        // Fermeture des dropdowns en cliquant ailleurs
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.dropdown')) {
                this.closeAllDropdowns();
            }
        });
        
        // Gestion des notifications
        const markAllReadBtn = document.querySelector('.mark-all-read');
        if (markAllReadBtn) {
            markAllReadBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.markAllNotificationsAsRead();
            });
        }
    }
    
    handleDropdowns() {
        const dropdowns = document.querySelectorAll('.dropdown');
        
        dropdowns.forEach(dropdown => {
            const trigger = dropdown.querySelector('a');
            const menu = dropdown.querySelector('.new-publication-types, .notifications-dropdown, .profile-dropdown');
            
            if (trigger && menu) {
                // Hover pour desktop
                dropdown.addEventListener('mouseenter', () => {
                    if (window.innerWidth > 767) {
                        this.showDropdown(menu);
                    }
                });
                
                dropdown.addEventListener('mouseleave', () => {
                    if (window.innerWidth > 767) {
                        this.hideDropdown(menu);
                    }
                });
                
                // Click pour mobile
                trigger.addEventListener('click', (e) => {
                    if (window.innerWidth <= 767) {
                        e.preventDefault();
                        this.toggleDropdown(menu);
                    }
                });
            }
        });
    }
    
    showDropdown(menu) {
        this.closeAllDropdowns();
        menu.style.opacity = '1';
        menu.style.visibility = 'visible';
        menu.style.transform = 'translateY(0)';
    }
    
    hideDropdown(menu) {
        menu.style.opacity = '0';
        menu.style.visibility = 'hidden';
        menu.style.transform = 'translateY(-10px)';
    }
    
    toggleDropdown(menu) {
        const isVisible = menu.style.opacity === '1';
        
        if (isVisible) {
            this.hideDropdown(menu);
        } else {
            this.showDropdown(menu);
        }
    }
    
    closeAllDropdowns() {
        const dropdownMenus = document.querySelectorAll('.new-publication-types, .notifications-dropdown, .profile-dropdown');
        dropdownMenus.forEach(menu => {
            this.hideDropdown(menu);
        });
    }
    
    toggleMobileMenu() {
        const sidebar = document.getElementById('side-bar');
        const menuBtn = document.getElementById('buttonClick');
        
        if (sidebar && menuBtn) {
            sidebar.classList.toggle('show-menu');
            menuBtn.classList.toggle('active');
            
            // Prévenir le scroll du body quand le menu est ouvert
            if (sidebar.classList.contains('show-menu')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }
    }
    
    async loadNotifications() {
        try {
            const response = await fetch('get_notifications.php');
            const data = await response.json();
            
            if (data.success) {
                this.updateNotificationBadges(data.unread_count);
                this.renderNotifications(data.notifications);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des notifications:', error);
        }
    }
    
    updateNotificationBadges(count) {
        const badges = document.querySelectorAll('#notification-count, #notification-count-mobile');
        
        badges.forEach(badge => {
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        });
    }
    
    renderNotifications(notifications) {
        const notificationsList = document.getElementById('notifications-list');
        
        if (!notificationsList) return;
        
        if (notifications.length === 0) {
            notificationsList.innerHTML = '<div class="no-notifications">Aucune notification</div>';
            return;
        }
        
        notificationsList.innerHTML = notifications.slice(0, 5).map(notification => `
            <div class="notification-item ${!notification.is_read ? 'unread' : ''}">
                <div class="notification-content">
                    <div class="notification-icon ${notification.type}">
                        ${this.getNotificationIcon(notification.type)}
                    </div>
                    <div class="notification-body">
                        <div class="notification-title">${this.escapeHtml(notification.title)}</div>
                        <div class="notification-message">${this.escapeHtml(notification.message)}</div>
                        <div class="notification-time">${this.formatTime(notification.created_at)}</div>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    getNotificationIcon(type) {
        const icons = {
            'welcome': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"/></svg>',
            'like': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"/></svg>',
            'comment': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/></svg>',
            'message': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/></svg>'
        };
        
        return icons[type] || icons['welcome'];
    }
    
    async markAllNotificationsAsRead() {
        try {
            const response = await fetch('mark_notifications_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ mark_all: true })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.updateNotificationBadges(0);
                this.loadNotifications();
            }
        } catch (error) {
            console.error('Erreur lors du marquage des notifications:', error);
        }
    }
    
    startNotificationPolling() {
        // Vérifier les nouvelles notifications toutes les 30 secondes
        setInterval(() => {
            this.loadNotifications();
        }, 30000);
    }
    
    handleMobileMenu() {
        // Fermer le menu mobile lors du redimensionnement vers desktop
        window.addEventListener('resize', () => {
            if (window.innerWidth > 767) {
                const sidebar = document.getElementById('side-bar');
                const menuBtn = document.getElementById('buttonClick');
                
                if (sidebar && sidebar.classList.contains('show-menu')) {
                    sidebar.classList.remove('show-menu');
                    menuBtn.classList.remove('active');
                    document.body.style.overflow = '';
                }
                
                this.closeAllDropdowns();
            }
        });
        
        // Fermer le menu mobile en cliquant sur un lien
        const sidebarLinks = document.querySelectorAll('.sidebar-menu a');
        sidebarLinks.forEach(link => {
            link.addEventListener('click', () => {
                if (window.innerWidth <= 767) {
                    this.toggleMobileMenu();
                }
            });
        });
    }
    
    // Utilitaires
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    formatTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));
        
        if (diffInMinutes < 1) {
            return 'À l\'instant';
        } else if (diffInMinutes < 60) {
            return `Il y a ${diffInMinutes} min`;
        } else if (diffInMinutes < 1440) {
            const hours = Math.floor(diffInMinutes / 60);
            return `Il y a ${hours}h`;
        } else {
            const days = Math.floor(diffInMinutes / 1440);
            return `Il y a ${days}j`;
        }
    }
}

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    new EnhancedNavbar();
});

// Gestion des messages non lus (pour le chat)
async function updateMessageCount() {
    try {
        const response = await fetch('chat_backend.php?action=get_unread_count');
        const data = await response.json();
        
        if (data.success) {
            const badges = document.querySelectorAll('#message-count, #message-count-mobile');
            const count = data.unread_count;
            
            badges.forEach(badge => {
                if (count > 0) {
                    badge.textContent = count > 99 ? '99+' : count;
                    badge.style.display = 'flex';
                } else {
                    badge.style.display = 'none';
                }
            });
        }
    } catch (error) {
        console.error('Erreur lors de la récupération du nombre de messages:', error);
    }
}

// Mettre à jour le compteur de messages toutes les 10 secondes
setInterval(updateMessageCount, 10000);

// Mettre à jour au chargement
document.addEventListener('DOMContentLoaded', updateMessageCount);
