* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-decoration: none;
    list-style: none;

}

body {
    margin: 0;
    padding: 0;
    font-family: "Inter", sans-serif;
    font-optical-sizing: auto;
    font-style: normal;
}

a {
    color: black;
}

.container {
    width: 1240px;
    padding-left: 60px;
    padding-right: 60px;
    margin-left: auto;
    margin-right: auto;
}

:root {
    --mainColor: #0048A6;
    --buttonColor: #267CF4;
    --lightGrey: #71717aaf;
    --backgoundcolor: #f1f5f9;
    --textColor: #000000;
    --colorparagraph: #6b7280;
    --greenColor: #059669;
    --experienceColor: #9333EA;
    --bgExperience: #f3e8ff;
    --problemeColor: #dc2626;
    --bgProbleme: #fee2e2;
    --activiteColor: #16a34a;
    --bgActivite: #dcfce7;
}

.head-profil {
    display: flex;
    align-items: center;
    gap: 25px;
}

.photo-profil {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: var(--lightGrey);
}

.script-profil {
    width: 700px;
    padding: 20px 0;
    flex: 1;
}

.script-profil h2 {
    font-size: 24px;
    font-weight: 600;
    color: var(--textColor);
    margin-bottom: 10px;
}

.script-profil p {
    font-size: 16px;
    color: var(--colorparagraph);
    line-height: 1.5;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    /* number of lines to show */
    line-clamp: 2;
    -webkit-box-orient: vertical;
}

.lieu {
    display: flex;
    align-items: center;
    color: grey;
    gap: 6px;
}

.bouttons {
    display: flex;
    align-items: center;
    margin-top: 20px;
    gap: 5px;
}

.bouttons .new-publication-close-overlay {
    position: absolute;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
    display: none;
}

.bouttons .new-publication-close-overlay.active {
    display: block
}

.bouttons .new-publication {
    position: relative;
}

.bouttons .new-publication-trigger {
    border-radius: 7px;
    border: 1px solid #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 7px 15px;
    cursor: pointer;
}

.bouttons .new-publication .new-publication-types {
    border-radius: 7px;
    border: 1px solid #6b7280;
    position: absolute;
    top: 45px;
    width: 100%;
    background-color: white;
    overflow: hidden;
    display: none;
}

.bouttons .new-publication .new-publication-types.active {
    display: block;
}

.bouttons .new-publication .new-publication-types .new-publication-type {
    padding: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 7px;
}

.bouttons .new-publication .new-publication-types .new-publication-type.experience:hover {
    color: var(--experienceColor);
    background-color: var(--bgExperience);
}

.bouttons .new-publication .new-publication-types .new-publication-type.probleme:hover {
    color: var(--problemeColor);
    background-color: var(--bgProbleme);
}

.bouttons .new-publication .new-publication-types .new-publication-type.activite:hover {
    color: var(--activiteColor);
    background-color: var(--bgActivite);
}

.bouttons>a {
    display: flex;
    width: fit-content;
    background-color: #059669;
    padding: 8px 10px;
    margin: 10px auto;
    color: white;
    border-radius: 7px;
    text-align: center;
    font-weight: 500;
    font-size: 15px;
    padding: 7px 7x;
    align-items: center;
    gap: 4px;
    cursor: pointer;
}

.bouttons>a.following {
    background-color: transparent;
    border: 1px solid #059669;
    color: #059669
}

.nombre {
    display: flex;
    align-items: center;
    /* gap: 2px; */
}

.nombre .abonnes .membres {
    width: 100px;
    border: 1px solid var(--lightGrey);
}

.nombre span {
    font-size: 16px;
    color: var(--textColor);
    font-weight: 600;
    padding: 5px 10px;
    border-radius: 5px;
}

.abonnes {
    border: 2px solid lightgray;
    padding: 8px 74px;
    border-radius: 9px 0 0 9px;
}

.membres {
    border: 2px solid lightgray;
    padding: 8px 74px;
    border-radius: 0 9px 9px 0;
}

.links {
    display: flex;
    align-items: center;
    border-radius: 8px;
    background: #f1f5f9;
    box-shadow: 2px 4PX 5PX 0PX rgb(128 128 128 / 24%);
    padding: 5px;
}

.links li {
    width: calc(100% / 3);
    text-align: center;
    padding: 8px 0;
    border-radius: 7px;
    cursor: pointer;
}

/* publications */


.posts {
    width: 768px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: auto;
}

.association-profile-tabs-triggers .tab-trigger.active {
    background-color: white;
}

.association-profile-tabs {
    padding-top: 20px;
}

.association-profile-tabs .tab {
    display: none;
}

.association-profile-tabs .tab.active {
    display: block;
}

.about {
    border: 1px solid #ddd;
    padding: 20px;
    border-radius: 12px;
}

.about .about-titre {
    font-size: 30px;
    font-weight: bolder;
}

.about .about-text {
    border-radius: 5px;
    border: 1px solid #ddd;
    padding: 10px;
    font-size: 18px;
    color: #999;
    margin-top: 10px;
}

.about .about-soustitre {
    padding-top: 10px;
    font-size: 18px;
    font-weight: 500;
}

.contact .contact-items {
    padding-top: 20px;
}

.contact .contact-titre {
    font-weight: bold;
    font-size: 30px;
}

.contact .contact-items .contact-item {
    display: flex;
    gap: 12px;
    align-items: center;
    color: var(--colorparagraph);
    font-size: 18px;
    padding-top: 5px;
}