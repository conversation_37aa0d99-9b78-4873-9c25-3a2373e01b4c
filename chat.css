* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-decoration: none;
    list-style: none;
   
 }
 body{
    margin: 0;
    padding: 0;
    font-family: "Inter", sans-serif;
    font-optical-sizing: auto;
    font-style: normal;
    overflow: hidden;
}

.container-full{
   width: 1240px;
    padding-left:60px;
    padding:10px;
    margin-right: auto;  
  
}

section {
  height: calc(100vh - 55px); /* 55px = navbar */
  display: flex;
  flex-direction: column;
  padding: 0 20px;
}
p, h2{
  margin: 0;
  padding: 0;
}
.titre{
 padding: 10px;
}

.parent-container {
  display: flex;
  flex: 1;
  height: 100%;
  overflow: hidden;
  border-top: 1px solid #ddd;
}

.sidebar {
  width: 320px;
  border-right: 1px solid #ccc;
  display: flex;
  flex-direction: column;
  background: #f9f9f9;
}

.cherche {
  padding: 10px;
  border-bottom: 1px solid #ccc;
}

.cherche input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 6px;
}

.chat-teams {
  flex: 1;
  overflow-y: auto;
}

.chat-teams ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.contenu-message {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.profile {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #bbb;
}

.resume {
  flex-grow: 1;
}

.nom-user {
  font-weight: bold;
}

.result-message {
  font-size: 14px;
  color: #666;
}

.time {
  font-size: 12px;
  color: #999;
}

/* --- Chat container --- */

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.chat-header {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #ccc;
  gap: 10px;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.chat-messages ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

li.message {
  max-width: 70%;
  padding: 10px 15px;
  border-radius: 8px;
  font-size: 15px;
  position: relative;
}

li.message .time {
  font-size: 11px;
  margin-top: 5px;
  text-align: right;
  color: #aaa;
}

li.message.sent {
  align-self: flex-start;
  background: #f1f1f1;
}

li.message.received {
  align-self: flex-end;
  background: #059669;
  color: white;
}

.chat-input {
  padding: 15px;
  border-top: 1px solid #ccc;
  display: flex;
  gap: 10px;
  background: white;
}

.chat-input input {
  flex: 1;
  height: 45px;
  border: 1px solid #ccc;
  border-radius: 6px;
  padding: 0 10px;
  font-size: 14px;
  margin: 0 10px;
}

.chat-input button {
  width: fit-content;
  height: 45px;
  background: var(--greenColor, #059669);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}
