<?php
session_start();
require_once 'config.php';

// Vérification de l'authentification
if (!isset($_SESSION['email'])) {
    header('Location: connexion.php');
    exit;
}

// Récupération de l'utilisateur
try {
    $stmt = $pdo->prepare("SELECT ID_UTILISATEUR FROM utilisateur WHERE EMAIL = :email");
    $stmt->execute([':email' => $_SESSION['email']]);
    $user = $stmt->fetch();
    
    if (!$user) {
        header('Location: logout.php');
        exit;
    }
    
    $userId = $user['ID_UTILISATEUR'];
} catch (PDOException $e) {
    header('Location: logout.php');
    exit;
}

// Créer la table des notifications si elle n'existe pas
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            type VARCHAR(50) NOT NULL,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            data JSON,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES utilisateur(ID_UTILISATEUR) ON DELETE CASCADE,
            INDEX idx_user_created (user_id, created_at),
            INDEX idx_user_read (user_id, is_read)
        )
    ");
} catch (PDOException $e) {
    // Table existe déjà ou erreur
}

// Traitement des actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['mark_read'])) {
        $notificationId = (int)$_POST['notification_id'];
        try {
            $stmt = $pdo->prepare("UPDATE notifications SET is_read = TRUE WHERE id = :id AND user_id = :user_id");
            $stmt->execute([':id' => $notificationId, ':user_id' => $userId]);
        } catch (PDOException $e) {
            // Erreur silencieuse
        }
    }
    
    if (isset($_POST['mark_all_read'])) {
        try {
            $stmt = $pdo->prepare("UPDATE notifications SET is_read = TRUE WHERE user_id = :user_id");
            $stmt->execute([':user_id' => $userId]);
        } catch (PDOException $e) {
            // Erreur silencieuse
        }
    }
    
    if (isset($_POST['delete_notification'])) {
        $notificationId = (int)$_POST['notification_id'];
        try {
            $stmt = $pdo->prepare("DELETE FROM notifications WHERE id = :id AND user_id = :user_id");
            $stmt->execute([':id' => $notificationId, ':user_id' => $userId]);
        } catch (PDOException $e) {
            // Erreur silencieuse
        }
    }
}

// Récupération des notifications
try {
    $stmt = $pdo->prepare("
        SELECT * FROM notifications 
        WHERE user_id = :user_id 
        ORDER BY created_at DESC 
        LIMIT 50
    ");
    $stmt->execute([':user_id' => $userId]);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $notifications = [];
}

// Si aucune notification, créer des exemples
if (empty($notifications)) {
    $sampleNotifications = [
        [
            'type' => 'welcome',
            'title' => 'Bienvenue sur YADFYAD !',
            'message' => 'Merci de rejoindre notre communauté d\'associations. Découvrez toutes les fonctionnalités disponibles.',
            'data' => json_encode(['action' => 'explore'])
        ],
        [
            'type' => 'like',
            'title' => 'Nouvelle interaction',
            'message' => 'Votre publication a reçu de nouveaux likes de la communauté.',
            'data' => json_encode(['post_id' => 1, 'likes_count' => 5])
        ],
        [
            'type' => 'comment',
            'title' => 'Nouveau commentaire',
            'message' => 'Quelqu\'un a commenté votre publication récente.',
            'data' => json_encode(['post_id' => 1, 'comment_id' => 1])
        ]
    ];
    
    try {
        foreach ($sampleNotifications as $notification) {
            $stmt = $pdo->prepare("
                INSERT INTO notifications (user_id, type, title, message, data) 
                VALUES (:user_id, :type, :title, :message, :data)
            ");
            $stmt->execute([
                ':user_id' => $userId,
                ':type' => $notification['type'],
                ':title' => $notification['title'],
                ':message' => $notification['message'],
                ':data' => $notification['data']
            ]);
        }
        
        // Recharger les notifications
        $stmt = $pdo->prepare("
            SELECT * FROM notifications 
            WHERE user_id = :user_id 
            ORDER BY created_at DESC 
            LIMIT 50
        ");
        $stmt->execute([':user_id' => $userId]);
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // Erreur silencieuse
    }
}

// Compter les notifications non lues
$unreadCount = count(array_filter($notifications, function($n) { return !$n['is_read']; }));
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifications | YADFYAD</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/styles/style.css">
    <link rel="stylesheet" href="assets/styles/responsive.css">
    <style>
        .notifications-container {
            max-width: 800px;
            margin: 40px auto;
            padding: 0 20px;
        }
        
        .notifications-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .notifications-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin: 0;
        }
        
        .notifications-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #ddd;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .notifications-stats {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .notifications-list {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .notification-item {
            padding: 20px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s ease;
            position: relative;
        }
        
        .notification-item:last-child {
            border-bottom: none;
        }
        
        .notification-item:hover {
            background: #f8f9fa;
        }
        
        .notification-item.unread {
            background: #f0f4ff;
            border-left: 4px solid #667eea;
        }
        
        .notification-content {
            display: flex;
            gap: 15px;
            align-items: flex-start;
        }
        
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        
        .notification-icon.welcome {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .notification-icon.like {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }
        
        .notification-icon.comment {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }
        
        .notification-icon.message {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
        }
        
        .notification-body {
            flex: 1;
        }
        
        .notification-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .notification-message {
            color: #666;
            line-height: 1.5;
            margin-bottom: 10px;
        }
        
        .notification-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #999;
        }
        
        .notification-actions {
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: color 0.3s ease;
        }
        
        .action-btn:hover {
            color: #667eea;
        }
        
        .no-notifications {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .no-notifications svg {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        @media (max-width: 767px) {
            .notifications-container {
                margin: 20px auto;
                padding: 0 15px;
            }
            
            .notifications-header {
                flex-direction: column;
                align-items: stretch;
                text-align: center;
            }
            
            .notifications-header h1 {
                font-size: 2rem;
            }
            
            .notifications-actions {
                justify-content: center;
            }
            
            .notification-item {
                padding: 15px;
            }
            
            .notification-content {
                gap: 10px;
            }
            
            .notification-meta {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }
            
            .notification-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php require_once "sections/navbar.php"; ?>

    <div class="notifications-container">
        <div class="notifications-header">
            <h1>Notifications</h1>
            <div class="notifications-actions">
                <?php if ($unreadCount > 0): ?>
                <form method="POST" style="display: inline;">
                    <button type="submit" name="mark_all_read" class="btn btn-primary">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 6L9 17l-5-5"/>
                        </svg>
                        Tout marquer comme lu
                    </button>
                </form>
                <?php endif; ?>
                <a href="settings.php" class="btn btn-secondary">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/>
                        <circle cx="12" cy="12" r="3"/>
                    </svg>
                    Paramètres
                </a>
            </div>
        </div>

        <div class="notifications-stats">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number"><?php echo count($notifications); ?></div>
                    <div class="stat-label">Total</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo $unreadCount; ?></div>
                    <div class="stat-label">Non lues</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo count($notifications) - $unreadCount; ?></div>
                    <div class="stat-label">Lues</div>
                </div>
            </div>
        </div>

        <?php if (empty($notifications)): ?>
            <div class="notifications-list">
                <div class="no-notifications">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M10.268 21a2 2 0 0 0 3.464 0"/>
                        <path d="M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326"/>
                    </svg>
                    <h3>Aucune notification</h3>
                    <p>Vous n'avez pas encore de notifications. Elles apparaîtront ici quand vous interagirez avec la communauté.</p>
                </div>
            </div>
        <?php else: ?>
            <div class="notifications-list">
                <?php foreach ($notifications as $notification): ?>
                    <div class="notification-item <?php echo !$notification['is_read'] ? 'unread' : ''; ?>">
                        <div class="notification-content">
                            <div class="notification-icon <?php echo $notification['type']; ?>">
                                <?php
                                $icons = [
                                    'welcome' => '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"/></svg>',
                                    'like' => '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"/></svg>',
                                    'comment' => '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/></svg>',
                                    'message' => '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/></svg>'
                                ];
                                echo $icons[$notification['type']] ?? $icons['welcome'];
                                ?>
                            </div>
                            
                            <div class="notification-body">
                                <div class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></div>
                                <div class="notification-message"><?php echo htmlspecialchars($notification['message']); ?></div>
                                
                                <div class="notification-meta">
                                    <span><?php echo date('d/m/Y à H:i', strtotime($notification['created_at'])); ?></span>
                                    <div class="notification-actions">
                                        <?php if (!$notification['is_read']): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                            <button type="submit" name="mark_read" class="action-btn" title="Marquer comme lu">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M20 6L9 17l-5-5"/>
                                                </svg>
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                        
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                            <button type="submit" name="delete_notification" class="action-btn" title="Supprimer" onclick="return confirm('Supprimer cette notification ?')">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M3 6h18"/>
                                                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                                                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                                                </svg>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script src="script.js"></script>
</body>
</html>
