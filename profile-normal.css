* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-decoration: none;
    list-style: none;
   
 }
 body{
    margin: 0;
    padding: 0;
    font-family: "Inter", sans-serif;
    font-optical-sizing: auto;
    font-style: normal;
}
a{
    color: black;
}
.container{
   width: 1240px;
    padding-left:160px;
    padding-right:160px;
    margin-left: auto;
    margin-right: auto;   
}
:root{
    --mainColor:#0048A6;
    --buttonColor:#267CF4;
    --lightGrey:#71717aaf;
    --backgoundcolor:#f1f5f9;
    --textColor:#000000;
    --colorparagraph:#6b7280;
    --greenColor:#059669;
}
.head-profil {
  margin-top: 100px;
    display: flex;
    align-items: center;
    flex-direction: column;
   
}
.photo-profil {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background-color: var(--lightGrey);
}
.script-profil{
  width: 700px;
  padding: 20px 0;
  flex: 1;
  text-align: center;
}
.script-profil h2{
    font-size: 24px;
    font-weight: 600;
    color: var(--textColor);
    margin-bottom: 10px;
}
.script-profil p{
    font-size: 16px;
    color: var(--colorparagraph);
    line-height: 1.5;
}
.infos{
    display: flex;
    align-items: center;
    justify-content: center;
    color: grey;
    gap: 20px;
}
.lieu{
  display: flex;
    align-items: center
}
.modifier{
   text-align: center;
}
.modifier a{
   color: var(--buttonColor);
   font-weight: 500;
   font-size: 16px;
   text-decoration: none;
   padding: 10px 20px;
   border-radius: 5px;
   background-color: var(--greenColor);
   color: white;
}

.envoyer-message{
text-align: center;}
.envoyer-message a{
   color: var(--buttonColor);
   font-weight: 500;
   font-size: 16px;
   text-decoration: none;
   padding: 10px 20px;
   border-radius: 5px;
   background-color: var(--greenColor);
   color: white;
}
footer {
   width: 100%;
   height: 55px;
   background-color: #f8f9fa;
   display: flex;
   align-items: center;
   justify-content: center;
   margin-top: 20px;
   border-top: 1px solid rgb(3 3 3 / 19%);
}
.copyright {
   margin-top: 21px;
   display: block;
   text-align: center;
   padding: 20px 0;
   color: var(--lightgray);
}