* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-decoration: none;
    list-style: none;
   
 }
 body{
    margin: 0;
    padding: 0;
    font-family: "Inter", sans-serif;
    font-optical-sizing: auto;
    font-style: normal;
}
a{
    color: black;
}
.container{
    width: 100%;
    max-width: 1230px;
    padding-left: 4%;
    padding-right: 4%;
    margin-left: auto;
    margin-right: auto;
}
:root{
    --mainColor:#0048A6;
    --buttonColor:#267CF4;
    --lightGrey:#71717aaf;
}
section{
   width: 100%;
   margin: 0;
   padding: 0;
   position: relative;

}
section.hero{
    background-image: linear-gradient(white, #c5e9fb);
    margin-top: 10px;
    width: 100%;
    display: flex;
    align-items: center;
    height: 100vh;
    justify-content: center;
    flex-wrap: wrap;
}
section .containe{
    width: 100%;
    padding-left:4%;
    padding-right:3%;
    margin-left: auto;
    margin-right: auto;
   
   
}
.contenu {
    width: 100%;
    display: flex;
}
.inctro{
    width: auto;
    margin-top:3%;
}
.inctro h1{
    font-size: 48px;
    font-weight: 700;
}
.inctro p{
    font-size: 20px;
    font-weight: 400;
    color: var(--lightGrey);
}
.rejoindre-button a{
   color: white;
    background-color: green;
    padding: 15px 25px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
    text-decoration: none;
    display: block;
    width: fit-content;
    margin-top: 30px;
}
.title{
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2%;
}
.title h2{
    font-size: 48px;
    letter-spacing: -2.4px;
    font-weight: 600;
    margin: auto;
}
.title p{
    font-size: 16px;
    font-weight: 400;
    margin: auto;
    color: var(--lightGrey);;
}
.carres{
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    grid-gap: 50px;
    text-align: left;
    margin: 38px 60px
}

.carres .column {
    padding: 20px 2.5rem;
    border: 1px solid #71717a4a;
    border-radius: 8px;
 }
 .carres .column span {
    display: block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(0, 128, 0, 0.187);
    padding: 12px;
 }
 .carres .column p{
    margin: auto;
    color: #71717a;
    font-size: 14px;
 }

 /* =====start explorer =====*/

.explorer .titre{
    width: 100%;
     display: block;
    text-align: center;
    padding: 15px;
}
.titre h2{
    font-size: 48px;
    font-weight: 600;
    letter-spacing: -2.4px;
    margin: auto;
} 
.titre p{
    font-size: 16px;
    font-weight: 300;
    color: var(--lightGrey);
    margin: auto;
}
.landing-posts{
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    margin-top: 20px;
    justify-items: center;
    gap: 20px;
}

.post{
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: auto;
    height: 225px;
    border: 1px solid #71717a54;
    border-radius: 12px;
    padding: 20px;
}
.post-header{
    display: flex;
    gap: 12px;
    align-items: center;
}
.avatar{
    width: 60px;
    height: 60px;
    border: 0.4px solid black;
    border-radius: 50%;
}
.post-header-content .post-header-name{
    font-weight: 600;
    font-size: 16px;

}
.post-header-content .post-header-time{
    font-size: 14px;
    font-weight:400;
    color:var(--lightGrey)
}
.description{
    display: flex;
    flex:1;
}
.description p{
    font-weight: 400;
    font-size: 15px;
    margin: 0;
}
.icone{
    display: flex;
    align-items: center;
    gap: 12px;
   
}
.like{
    display: flex;
    align-items: center;
    gap: 3px;
}
.commenter{
    display: flex;
    align-items: center;
    gap: 3px;
}

/* ASSOCIATIONS */

section.association{
    margin-top: 30px;
}
.association .titre{
    text-align: center;
    padding: 15px;
}
.slides{
    margin: 30px 0;
    display: flex;
    gap: 130px;
}
.slides .photo-profil{
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: center;
}
.avatar{
    overflow:hidden;
}
.avatar img{
 width:100%;
}

/* REJOINDRE */
section.rejoindre {
width: 100%;
height: 380px;
display: flex;
position: relative;
padding: 50PX 0 0 0;
BACKGROUND: var(--buttonColor);
 TEXT-ALIGN: center;
}
.info h2{
    font-size: 40px;
    font-weight: 600;
    letter-spacing: -2px;
    margin: auto;
    color: white;
}  
.info p {
    width: 58%;
    margin: auto;
    padding: 20px 0;
    font-weight: 300; 
    color: white;
} 
.rejoindre .boutton{
    padding: 20PX 60px 0 0;
}

.rejoindre a{
    color: black;
    background-color: #10B77F;
    padding: 10px 20px;
    font-size: 12PX;
    font-weight: 500;
    border-radius: 8px;
    TEXT-ALIGN: center;
}

/* START FOOTER  */
footer{
    width: 100%;
    height: auto;
    position: relative;
  }
  .logo p{
    color: var(--lightGrey);
    font-size: 14px;
  }
  footer .footer-link{
    display: flex;
    justify-content: space-between;
    gap: 20px;
    padding: 25px 0;
    
    width: 100%;
    color: var(--lightGrey);
  }
  footer .footer-link .column ul{
    display: flex;
    flex-direction: column;
    gap: 20px;
    list-style: none;
  }
  footer .footer-link .column ul li a{
    text-decoration: none;
    color: var(--lightGrey);
    
}
.copyright{
    display: block;
    border-top: 1px solid var(--lightGrey);
    width: 100%;
    text-align: center;
    padding: 20px 0;
    color: var(--lightGrey);
}
footer .footer-link.column h3{
    color: var(--lightGrey);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Mobile Styles (320px - 767px) */
@media (max-width: 767px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }

    section.hero {
        height: auto;
        min-height: 100vh;
        padding: 40px 0;
    }

    .contenu {
        flex-direction: column;
        text-align: center;
        gap: 30px;
    }

    .inctro {
        width: 100%;
        margin-top: 0;
        order: 2;
    }

    .inctro h1 {
        font-size: 32px;
        line-height: 1.2;
        margin-bottom: 15px;
    }

    .inctro p {
        font-size: 18px;
        line-height: 1.5;
        margin-bottom: 25px;
    }

    .img {
        width: 100%;
        order: 1;
    }

    .img img {
        width: 100%;
        max-width: 350px;
        height: auto;
    }

    .rejoindre-button {
        text-align: center;
    }

    .rejoindre-button a {
        display: inline-block;
        width: auto;
        padding: 15px 30px;
        font-size: 18px;
    }

    .title h2 {
        font-size: 28px;
        text-align: center;
    }

    .titre p {
        font-size: 16px;
        text-align: center;
        padding: 0 10px;
    }

    .carres {
        grid-template-columns: 1fr;
        gap: 25px;
        margin: 30px 0;
    }

    .carre {
        padding: 25px;
        text-align: center;
    }

    .carre h3 {
        font-size: 20px;
        margin-bottom: 15px;
    }

    .carre p {
        font-size: 16px;
        line-height: 1.6;
    }

    .landing-posts {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-top: 30px;
    }

    .post {
        width: 100%;
        max-width: none;
        padding: 20px;
        height: auto;
        min-height: 200px;
    }

    .post-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .avatar {
        width: 50px;
        height: 50px;
        margin: 0 auto;
    }

    .post-header-content {
        text-align: center;
    }

    .description {
        text-align: left;
        margin-top: 15px;
    }

    .slides {
        flex-direction: column;
        gap: 30px;
        align-items: center;
    }

    .slide {
        width: 100%;
        max-width: 300px;
        text-align: center;
    }

    .slide img {
        width: 80px;
        height: 80px;
    }

    /* Footer responsive */
    footer .footer-link {
        flex-direction: column;
        gap: 30px;
        text-align: center;
    }

    .info p {
        width: 90%;
        font-size: 16px;
    }

    section.rejoindre {
        height: auto;
        padding: 40px 0;
    }

    .info h2 {
        font-size: 28px;
    }
}

