<?php
/**
 * API pour marquer les notifications comme lues
 */

session_start();
require_once 'config.php';

header('Content-Type: application/json');

// Vérification de l'authentification
if (!isset($_SESSION['email'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Non authentifié']);
    exit;
}

// Vérification de la méthode POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Méthode non autorisée']);
    exit;
}

// Récupération de l'utilisateur
try {
    $stmt = $pdo->prepare("SELECT ID_UTILISATEUR FROM utilisateur WHERE EMAIL = :email");
    $stmt->execute([':email' => $_SESSION['email']]);
    $user = $stmt->fetch();
    
    if (!$user) {
        http_response_code(401);
        echo json_encode(['success' => false, 'error' => 'Utilisateur non trouvé']);
        exit;
    }
    
    $userId = $user['ID_UTILISATEUR'];
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Erreur de base de données']);
    exit;
}

// Récupération des données JSON
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Données JSON invalides']);
    exit;
}

try {
    if (isset($input['mark_all']) && $input['mark_all']) {
        // Marquer toutes les notifications comme lues
        $stmt = $pdo->prepare("
            UPDATE notifications 
            SET is_read = TRUE 
            WHERE user_id = :user_id AND is_read = FALSE
        ");
        $stmt->execute([':user_id' => $userId]);
        
        $affectedRows = $stmt->rowCount();
        
        echo json_encode([
            'success' => true,
            'message' => 'Toutes les notifications ont été marquées comme lues',
            'affected_rows' => $affectedRows
        ]);
        
    } elseif (isset($input['notification_id'])) {
        // Marquer une notification spécifique comme lue
        $notificationId = (int)$input['notification_id'];
        
        $stmt = $pdo->prepare("
            UPDATE notifications 
            SET is_read = TRUE 
            WHERE id = :id AND user_id = :user_id
        ");
        $stmt->execute([
            ':id' => $notificationId,
            ':user_id' => $userId
        ]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Notification marquée comme lue'
            ]);
        } else {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'error' => 'Notification non trouvée'
            ]);
        }
        
    } else {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Paramètres manquants'
        ]);
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Erreur lors de la mise à jour des notifications'
    ]);
}
?>
