<?php
session_start();
require_once "config.php";

// Check if user is logged in as association
if (!isset($_SESSION["email"]) || $_SESSION["type"] !== 'association') {
    header("Location: connexion.php");
    exit;
}

// Get association info
$stmt = $pdo->prepare("SELECT * FROM association WHERE EMAIL = :email");
$stmt->execute([':email' => $_SESSION["email"]]);
$association = $stmt->fetch();

if (!$association) {
    header("Location: connexion.php");
    exit;
}

// If already verified, redirect to profile
if ($association['VERIFIE']) {
    header("Location: profile-association.php");
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vérification en cours | YADFYAD</title>
    <link rel="stylesheet" href="assets/styles/style.css">
    <link rel="stylesheet" href="authentification.css">
    <style>
        .pending-container {
            max-width: 600px;
            margin: 100px auto;
            padding: 40px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .pending-icon {
            width: 80px;
            height: 80px;
            background: #FEF3C7;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 40px;
        }
        
        .pending-title {
            font-size: 24px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 16px;
        }
        
        .pending-message {
            color: #6B7280;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        
        .association-info {
            background: #F9FAFB;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: left;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #E5E7EB;
        }
        
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .info-label {
            font-weight: 600;
            color: #374151;
        }
        
        .info-value {
            color: #6B7280;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-pending {
            background: #FEF3C7;
            color: #92400E;
        }
        
        .actions {
            margin-top: 30px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-secondary {
            background: #F3F4F6;
            color: #374151;
        }
        
        .btn-secondary:hover {
            background: #E5E7EB;
        }
    </style>
</head>
<body>
    <div class="pending-container">
        <div class="pending-icon">⏳</div>
        
        <h1 class="pending-title">Vérification en cours</h1>
        
        <p class="pending-message">
            Votre demande d'inscription a été soumise avec succès. Notre équipe examine actuellement 
            votre association pour s'assurer qu'elle respecte nos critères de qualité et de légitimité.
        </p>
        
        <div class="association-info">
            <div class="info-row">
                <span class="info-label">Association:</span>
                <span class="info-value"><?= htmlspecialchars($association['NOM_ASSOCIATION']) ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Email:</span>
                <span class="info-value"><?= htmlspecialchars($association['EMAIL']) ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Domaine:</span>
                <span class="info-value"><?= htmlspecialchars($association['DOMAINE'] ?? 'Non spécifié') ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Statut:</span>
                <span class="status-badge status-pending"><?= htmlspecialchars($association['STATUT']) ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Date de soumission:</span>
                <span class="info-value"><?= date('d/m/Y à H:i', strtotime($association['DATE_CREATION'])) ?></span>
            </div>
        </div>
        
        <div style="background: #EFF6FF; padding: 16px; border-radius: 8px; margin: 20px 0;">
            <p style="margin: 0; color: #1E40AF; font-size: 14px;">
                <strong>💡 Que se passe-t-il ensuite ?</strong><br>
                • Notre équipe vérifie vos informations (24-48h)<br>
                • Vous recevrez un email de confirmation<br>
                • Une fois approuvée, vous pourrez accéder à toutes les fonctionnalités
            </p>
        </div>
        
        <div class="actions">
            <a href="logout.php" class="btn btn-secondary">Se déconnecter</a>
        </div>
        
        <p style="margin-top: 30px; font-size: 14px; color: #6B7280;">
            Des questions ? Contactez-nous à <strong><EMAIL></strong>
        </p>
    </div>
</body>
</html>
