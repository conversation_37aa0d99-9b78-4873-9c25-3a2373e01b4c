* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
 }
 
 a {
    text-decoration: none;
 }
 
 body {
    margin: 0;
    padding: 0;
    text-decoration: none;
    list-style: none;
    font-family: 'Inter', sans-serif;
 
 }
 
 :root {
    --lightgray: #71717A;
    --colorblue: rgba(0, 68, 255, 0.75);
 }
 
 header {
    width: 100%;
    margin: 0;
    padding: 0;
    position: relative;
    box-shadow: 0px 0.5px 6px 0px rgba(128, 128, 128, 0.748);
 }
 
 .container {
    width: 1230px;
    padding-left: 30px;
    padding-right: 30px;
    margin-left: auto;
    margin-right: auto;
 }





    .btn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s;
    cursor: pointer;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    }

    .btn-outline {
    background: white;
    color: #374151;
    border: 1px solid #d1d5db;
    }

    .btn-outline:hover {
    background: #f9fafb;
    }

    .btn-primary {
    background: #059669;
    color: white;
    border: 1px solid #059669;
    }

    .btn-primary:hover {
    background: #047857;
    }

    /* Main Content */
    
    .page-title {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    }

    .page-description {
    color: #6b7280;
    }

    /* Search Section */
    .search-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
    }

    .search-container {
    position: relative;
    flex: 1;
    }

    .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1rem;
    height: 1rem;
    color: #6b7280;
    }

    .search-input {
    width: 444px;
    padding: 8px 31px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
    }

    .search-input:focus {
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
    }

    /* Cards Grid */
    .cards-grid {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
    }

    .cards-grid-associations {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    .cards-grid-publications {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }

    .card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s;
    }

    .card:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .card-image {
    width: 100%;
    aspect-ratio: 16/9;
    background: #f3f4f6;
    object-fit: cover;
    }

    .card-content {
    padding: 1.5rem;
    }

    .card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    }

    .avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background: #f3f4f6;
    object-fit: cover;
    }

    .avatar-large {
    width: 5rem;
    height: 5rem;
    }

    .card-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    }

    .card-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    }

    .card-description {
    color: #374151;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    }

    .card-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    }

    .action-btn {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    background: none;
    border: none;
    color: #6b7280;
    font-size: 0.875rem;
    cursor: pointer;
    transition: color 0.2s;
    }

    .action-btn:hover {
    color: #059669;
    }

    .action-btn.share {
    margin-left: auto;
    }

    .action-icon {
    width: 1rem;
    height: 1rem;
    }

    /* Association Card */
    .association-card {
    text-align: center;
    }

    .association-card .card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    }

    .association-card .avatar {
    margin-bottom: 1rem;
    }

    .association-card .card-description {
    margin-bottom: 1rem;
    text-align: center;
    }

    /* Load More */
    .load-more {
    display: flex;
    justify-content: center;
    }

    /* Footer */
    footer {
   width: 100%;
   height: 55px;
   background-color: #f8f9fa;
   display: flex;
   align-items: center;
   justify-content: center;
   margin-top: 20px;
  border-top: 1px solid lightslategray;
color: grey;
   font-size: 14px;
   }


    /* Responsive */
    @media (min-width: 768px) {
    .nav {
    display: flex;
    }

    .search-section {
    flex-direction: row;
    }

    .footer-content {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
    }

    .cards-grid-publications {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    }
    }

    @media (min-width: 1024px) {
    .cards-grid-publications {
    grid-template-columns: repeat(3, 1fr);
    }

    .cards-grid-associations {
    grid-template-columns: repeat(4, 1fr);
    }
    }