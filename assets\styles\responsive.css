/* 
 * YADFYAD Responsive Design System
 * Mobile-first approach with breakpoints for all devices
 */

/* ===== BREAKPOINTS ===== */
/* 
 * Mobile: 320px - 767px
 * Tablet: 768px - 1023px  
 * Desktop: 1024px+
 */

/* ===== GLOBAL RESPONSIVE UTILITIES ===== */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Mobile First - Base Styles */
@media (max-width: 767px) {
    .container {
        padding: 0 10px;
    }
    
    /* Hide desktop elements on mobile */
    .desktop-only {
        display: none !important;
    }
    
    /* Show mobile elements */
    .mobile-only {
        display: block !important;
    }
    
    /* Typography adjustments */
    h1 { font-size: 24px !important; }
    h2 { font-size: 20px !important; }
    h3 { font-size: 18px !important; }
    
    /* Button adjustments */
    .btn, button, input[type="submit"] {
        width: 100%;
        padding: 12px !important;
        font-size: 16px !important;
    }
    
    /* Form adjustments */
    input, textarea, select {
        width: 100% !important;
        font-size: 16px !important; /* Prevents zoom on iOS */
    }
}

/* Tablet Styles */
@media (min-width: 768px) and (max-width: 1023px) {
    .container {
        padding: 0 20px;
    }
    
    .tablet-hide {
        display: none !important;
    }
}

/* Desktop Styles */
@media (min-width: 1024px) {
    .container {
        padding: 0 30px;
    }
    
    .mobile-only {
        display: none !important;
    }
    
    .desktop-only {
        display: block !important;
    }
}

/* ===== NAVIGATION RESPONSIVE ===== */
@media (max-width: 767px) {
    header nav {
        padding: 0 10px !important;
    }
    
    header nav .logo {
        width: auto !important;
        flex-shrink: 0;
    }
    
    header nav .logo img {
        height: 40px !important;
    }
    
    header nav .link,
    header nav .icons,
    header nav .button {
        display: none !important;
    }
    
    .menu-mobile {
        display: flex !important;
        margin-left: auto;
    }
    
    .side-bar {
        position: fixed !important;
        top: 55px !important;
        left: -100% !important;
        width: 100% !important;
        height: calc(100vh - 55px) !important;
        background: white !important;
        z-index: 1000 !important;
        transition: left 0.3s ease !important;
        padding: 20px !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
    }
    
    .side-bar.show-menu {
        left: 0 !important;
    }
    
    .side-bar ul {
        display: flex !important;
        flex-direction: column !important;
        gap: 15px !important;
    }
    
    .side-bar ul li a {
        display: flex !important;
        align-items: center !important;
        gap: 10px !important;
        padding: 15px !important;
        border-radius: 8px !important;
        background: #f8f9fa !important;
        color: #333 !important;
        font-size: 16px !important;
        font-weight: 500 !important;
    }
}

/* ===== HERO SECTION RESPONSIVE ===== */
@media (max-width: 767px) {
    section.hero {
        height: auto !important;
        min-height: 100vh !important;
        padding: 60px 0 40px !important;
    }
    
    .contenu {
        flex-direction: column !important;
        text-align: center !important;
    }
    
    .inctro {
        width: 100% !important;
        margin-top: 0 !important;
        order: 2;
    }
    
    .inctro h1 {
        font-size: 28px !important;
        line-height: 1.2 !important;
        margin-bottom: 15px !important;
    }
    
    .inctro p {
        font-size: 16px !important;
        line-height: 1.5 !important;
        margin-bottom: 20px !important;
    }
    
    .img {
        width: 100% !important;
        order: 1;
        margin-bottom: 30px !important;
    }
    
    .img img {
        width: 100% !important;
        max-width: 300px !important;
        height: auto !important;
    }
    
    .rejoindre-button {
        text-align: center !important;
    }
    
    .rejoindre-button a {
        display: inline-block !important;
        width: auto !important;
        padding: 15px 30px !important;
    }
}

/* ===== CARDS AND GRIDS RESPONSIVE ===== */
@media (max-width: 767px) {
    .carres {
        grid-template-columns: 1fr !important;
        gap: 20px !important;
        margin: 20px 0 !important;
    }
    
    .landing-posts {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
    }
    
    .posts {
        width: 100% !important;
    }
    
    .post {
        margin: 0 0 15px !important;
    }
    
    .slides {
        flex-direction: column !important;
        gap: 20px !important;
        align-items: center !important;
    }
    
    .photo-profil {
        width: 100px !important;
    }
}

@media (min-width: 768px) and (max-width: 1023px) {
    .carres {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 30px !important;
    }
    
    .landing-posts {
        grid-template-columns: 1fr !important;
        gap: 20px !important;
    }
    
    .slides {
        gap: 40px !important;
        justify-content: center !important;
        flex-wrap: wrap !important;
    }
}

/* ===== PROFILE PAGES RESPONSIVE ===== */
@media (max-width: 767px) {
    .head-profil {
        flex-direction: column !important;
        text-align: center !important;
        gap: 15px !important;
    }
    
    .photo-profil {
        width: 120px !important;
        height: 120px !important;
    }
    
    .script-profil {
        width: 100% !important;
        padding: 10px 0 !important;
    }
    
    .bouttons {
        width: 100% !important;
        flex-direction: row !important;
        justify-content: center !important;
        gap: 10px !important;
        margin-top: 15px !important;
    }
    
    .links-profile ul {
        flex-direction: column !important;
        gap: 10px !important;
    }
    
    .links-profile ul li {
        text-align: center !important;
    }
}

/* ===== FORMS RESPONSIVE ===== */
@media (max-width: 767px) {
    .connexion .info {
        flex-direction: column !important;
        gap: 30px !important;
    }
    
    .picture {
        order: 2 !important;
    }
    
    .picture img {
        width: 100% !important;
        max-width: 300px !important;
        height: auto !important;
    }
    
    .connecte {
        order: 1 !important;
        padding: 20px !important;
        box-shadow: none !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 8px !important;
    }
    
    .form-group {
        padding: 10px 0 !important;
    }
    
    .information {
        width: 100% !important;
        padding: 15px !important;
    }
}

/* ===== CHAT RESPONSIVE ===== */
@media (max-width: 767px) {
    .parent-container {
        flex-direction: column !important;
    }
    
    .sidebar {
        width: 100% !important;
        height: 200px !important;
        border-right: none !important;
        border-bottom: 1px solid #ccc !important;
    }
    
    .chat-container {
        flex: 1 !important;
    }
    
    .chat-messages {
        height: calc(100vh - 300px) !important;
    }
}

/* ===== ADMIN PANEL RESPONSIVE ===== */
@media (max-width: 767px) {
    .admin-container {
        flex-direction: column !important;
    }
    
    .admin-container .sidebar {
        width: 100% !important;
        height: auto !important;
        border-right: none !important;
        border-bottom: 1px solid #ddd !important;
    }
    
    .admin-container .sidebar .logo {
        height: 50px !important;
        font-size: 16px !important;
    }
    
    .admin-container .sidebar ul.navlinks {
        flex-direction: row !important;
        overflow-x: auto !important;
        padding: 5px !important;
    }
    
    .admin-container .sidebar ul.navlinks li {
        flex-shrink: 0 !important;
    }
    
    .main-content {
        padding: 15px !important;
    }
    
    .card-table {
        overflow-x: auto !important;
    }
    
    .card-table table {
        min-width: 600px !important;
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center-mobile {
    text-align: left;
}

.flex-column-mobile {
    display: flex;
}

@media (max-width: 767px) {
    .text-center-mobile {
        text-align: center !important;
    }
    
    .flex-column-mobile {
        flex-direction: column !important;
    }
    
    .w-full-mobile {
        width: 100% !important;
    }
    
    .hidden-mobile {
        display: none !important;
    }
    
    .block-mobile {
        display: block !important;
    }
}

/* ===== ANIMATIONS FOR MOBILE ===== */
@media (max-width: 767px) {
    .side-bar {
        transition: left 0.3s ease-in-out !important;
    }
    
    .post, .card {
        transition: transform 0.2s ease !important;
    }
    
    .post:active, .card:active {
        transform: scale(0.98) !important;
    }
}
