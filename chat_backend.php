<?php
/**
 * Chat Backend - API REST pour le système de chat
 * Sans framework, PHP vanilla uniquement
 */

session_start();
require_once 'config.php';

// Configuration des headers pour API REST
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Gestion des requêtes OPTIONS (CORS)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Vérification de l'authentification
if (!isset($_SESSION['email'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Non authentifié']);
    exit;
}

// Récupération de l'utilisateur actuel
try {
    $stmt = $pdo->prepare("SELECT ID_UTILISATEUR, NOM, PRENOM, EMAIL FROM utilisateur WHERE EMAIL = :email");
    $stmt->execute([':email' => $_SESSION['email']]);
    $currentUser = $stmt->fetch();
    
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['error' => 'Utilisateur non trouvé']);
        exit;
    }
    
    $currentUserId = $currentUser['ID_UTILISATEUR'];
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur de base de données']);
    exit;
}

// Routage des actions
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'get_conversations':
        getConversations($pdo, $currentUserId);
        break;
        
    case 'get_messages':
        $receiverId = $_GET['receiver_id'] ?? null;
        if (!$receiverId) {
            http_response_code(400);
            echo json_encode(['error' => 'receiver_id requis']);
            exit;
        }
        getMessages($pdo, $currentUserId, $receiverId);
        break;
        
    case 'send_message':
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
            exit;
        }
        sendMessage($pdo, $currentUserId);
        break;
        
    case 'mark_as_read':
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
            exit;
        }
        markAsRead($pdo, $currentUserId);
        break;
        
    case 'search_users':
        $query = $_GET['q'] ?? '';
        searchUsers($pdo, $currentUserId, $query);
        break;
        
    case 'get_user_info':
        $userId = $_GET['user_id'] ?? null;
        if (!$userId) {
            http_response_code(400);
            echo json_encode(['error' => 'user_id requis']);
            exit;
        }
        getUserInfo($pdo, $userId);
        break;

    case 'get_unread_count':
        getUnreadMessageCount($pdo, $currentUserId);
        break;

    default:
        http_response_code(404);
        echo json_encode(['error' => 'Action non trouvée']);
        break;
}

/**
 * Récupère la liste des conversations de l'utilisateur
 */
function getConversations($pdo, $currentUserId) {
    try {
        $sql = "
            SELECT DISTINCT
                CASE 
                    WHEN c.user1_id = :current_user THEN c.user2_id 
                    ELSE c.user1_id 
                END as other_user_id,
                u.NOM,
                u.PRENOM,
                u.EMAIL,
                m.message as last_message,
                m.created_at as last_message_time,
                m.sender_id as last_sender_id,
                COUNT(CASE WHEN m2.is_read = FALSE AND m2.receiver_id = :current_user2 THEN 1 END) as unread_count
            FROM conversations c
            LEFT JOIN utilisateur u ON (
                CASE 
                    WHEN c.user1_id = :current_user3 THEN c.user2_id 
                    ELSE c.user1_id 
                END = u.ID_UTILISATEUR
            )
            LEFT JOIN chater m ON c.last_message_id = m.ID_MESSAGE
            LEFT JOIN chater m2 ON (
                (m2.sender_id = u.ID_UTILISATEUR AND m2.receiver_id = :current_user4) OR
                (m2.sender_id = :current_user5 AND m2.receiver_id = u.ID_UTILISATEUR)
            )
            WHERE c.user1_id = :current_user6 OR c.user2_id = :current_user7
            GROUP BY other_user_id, u.NOM, u.PRENOM, u.EMAIL, m.message, m.created_at, m.sender_id
            ORDER BY c.last_activity DESC
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':current_user' => $currentUserId,
            ':current_user2' => $currentUserId,
            ':current_user3' => $currentUserId,
            ':current_user4' => $currentUserId,
            ':current_user5' => $currentUserId,
            ':current_user6' => $currentUserId,
            ':current_user7' => $currentUserId
        ]);
        
        $conversations = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Formatage des données
        $formattedConversations = array_map(function($conv) {
            return [
                'user_id' => $conv['other_user_id'],
                'name' => $conv['PRENOM'] . ' ' . $conv['NOM'],
                'email' => $conv['EMAIL'],
                'last_message' => $conv['last_message'] ?? '',
                'last_message_time' => $conv['last_message_time'] ? date('H:i', strtotime($conv['last_message_time'])) : '',
                'unread_count' => (int)$conv['unread_count'],
                'is_last_sender' => $conv['last_sender_id'] == $_SESSION['current_user_id'] ?? false
            ];
        }, $conversations);
        
        echo json_encode([
            'success' => true,
            'conversations' => $formattedConversations
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des conversations']);
    }
}

/**
 * Récupère les messages d'une conversation
 */
function getMessages($pdo, $currentUserId, $receiverId) {
    try {
        $sql = "
            SELECT 
                m.ID_MESSAGE,
                m.sender_id,
                m.receiver_id,
                m.message,
                m.message_type,
                m.is_read,
                m.created_at,
                u.NOM,
                u.PRENOM
            FROM chater m
            JOIN utilisateur u ON m.sender_id = u.ID_UTILISATEUR
            WHERE 
                (m.sender_id = :current_user AND m.receiver_id = :receiver_id) OR
                (m.sender_id = :receiver_id2 AND m.receiver_id = :current_user2)
            ORDER BY m.created_at ASC
            LIMIT 100
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':current_user' => $currentUserId,
            ':current_user2' => $currentUserId,
            ':receiver_id' => $receiverId,
            ':receiver_id2' => $receiverId
        ]);
        
        $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Formatage des messages
        $formattedMessages = array_map(function($msg) use ($currentUserId) {
            return [
                'id' => $msg['ID_MESSAGE'],
                'message' => $msg['message'],
                'sender_id' => $msg['sender_id'],
                'sender_name' => $msg['PRENOM'] . ' ' . $msg['NOM'],
                'is_sent' => $msg['sender_id'] == $currentUserId,
                'is_read' => (bool)$msg['is_read'],
                'time' => date('H:i', strtotime($msg['created_at'])),
                'date' => date('Y-m-d', strtotime($msg['created_at'])),
                'type' => $msg['message_type']
            ];
        }, $messages);
        
        echo json_encode([
            'success' => true,
            'messages' => $formattedMessages
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des messages']);
    }
}

/**
 * Envoie un nouveau message
 */
function sendMessage($pdo, $currentUserId) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['receiver_id']) || !isset($input['message'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Données manquantes']);
        return;
    }
    
    $receiverId = (int)$input['receiver_id'];
    $message = trim($input['message']);
    $messageType = $input['type'] ?? 'text';
    
    if (empty($message)) {
        http_response_code(400);
        echo json_encode(['error' => 'Message vide']);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        // Insérer le message
        $stmt = $pdo->prepare("
            INSERT INTO chater (sender_id, receiver_id, message, message_type) 
            VALUES (:sender_id, :receiver_id, :message, :message_type)
        ");
        $stmt->execute([
            ':sender_id' => $currentUserId,
            ':receiver_id' => $receiverId,
            ':message' => $message,
            ':message_type' => $messageType
        ]);
        
        $messageId = $pdo->lastInsertId();
        
        // Créer ou mettre à jour la conversation
        $stmt = $pdo->prepare("
            INSERT INTO conversations (user1_id, user2_id, last_message_id) 
            VALUES (LEAST(:user1, :user2), GREATEST(:user1_2, :user2_2), :message_id)
            ON DUPLICATE KEY UPDATE 
                last_message_id = :message_id_2,
                last_activity = CURRENT_TIMESTAMP
        ");
        $stmt->execute([
            ':user1' => $currentUserId,
            ':user1_2' => $currentUserId,
            ':user2' => $receiverId,
            ':user2_2' => $receiverId,
            ':message_id' => $messageId,
            ':message_id_2' => $messageId
        ]);
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message_id' => $messageId,
            'time' => date('H:i')
        ]);
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de l\'envoi du message']);
    }
}

/**
 * Marque les messages comme lus
 */
function markAsRead($pdo, $currentUserId) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['sender_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'sender_id requis']);
        return;
    }
    
    $senderId = (int)$input['sender_id'];
    
    try {
        $stmt = $pdo->prepare("
            UPDATE chater 
            SET is_read = TRUE 
            WHERE sender_id = :sender_id AND receiver_id = :receiver_id AND is_read = FALSE
        ");
        $stmt->execute([
            ':sender_id' => $senderId,
            ':receiver_id' => $currentUserId
        ]);
        
        echo json_encode(['success' => true]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la mise à jour']);
    }
}

/**
 * Recherche d'utilisateurs
 */
function searchUsers($pdo, $currentUserId, $query) {
    try {
        $stmt = $pdo->prepare("
            SELECT ID_UTILISATEUR, NOM, PRENOM, EMAIL 
            FROM utilisateur 
            WHERE ID_UTILISATEUR != :current_user 
            AND (NOM LIKE :query OR PRENOM LIKE :query OR EMAIL LIKE :query)
            LIMIT 10
        ");
        $stmt->execute([
            ':current_user' => $currentUserId,
            ':query' => '%' . $query . '%'
        ]);
        
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $formattedUsers = array_map(function($user) {
            return [
                'id' => $user['ID_UTILISATEUR'],
                'name' => $user['PRENOM'] . ' ' . $user['NOM'],
                'email' => $user['EMAIL']
            ];
        }, $users);
        
        echo json_encode([
            'success' => true,
            'users' => $formattedUsers
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la recherche']);
    }
}

/**
 * Récupère les informations d'un utilisateur
 */
function getUserInfo($pdo, $userId) {
    try {
        $stmt = $pdo->prepare("
            SELECT ID_UTILISATEUR, NOM, PRENOM, EMAIL, DESCRIPTION 
            FROM utilisateur 
            WHERE ID_UTILISATEUR = :user_id
        ");
        $stmt->execute([':user_id' => $userId]);
        
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            http_response_code(404);
            echo json_encode(['error' => 'Utilisateur non trouvé']);
            return;
        }
        
        echo json_encode([
            'success' => true,
            'user' => [
                'id' => $user['ID_UTILISATEUR'],
                'name' => $user['PRENOM'] . ' ' . $user['NOM'],
                'email' => $user['EMAIL'],
                'description' => $user['DESCRIPTION']
            ]
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des informations']);
    }
}

/**
 * Récupère le nombre de messages non lus
 */
function getUnreadMessageCount($pdo, $currentUserId) {
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as unread_count
            FROM chater
            WHERE receiver_id = :user_id AND is_read = FALSE
        ");
        $stmt->execute([':user_id' => $currentUserId]);

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $unreadCount = (int)$result['unread_count'];

        echo json_encode([
            'success' => true,
            'unread_count' => $unreadCount
        ]);

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération du nombre de messages non lus']);
    }
}
?>
