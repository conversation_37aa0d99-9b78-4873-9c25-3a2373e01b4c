<?php
session_start();
require_once "config.php";

// Check if user is logged in
if (!isset($_SESSION["email"])) {
    header("Location: connexion.php");
    exit;
}

$error = "";
$success = "";

// Get user information
$stmt = $pdo->prepare("SELECT * FROM utilisateur WHERE EMAIL = :email");
$stmt->execute([':email' => $_SESSION["email"]]);
$user = $stmt->fetch();

if (!$user) {
    header("Location: connexion.php");
    exit;
}

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $nom = trim($_POST['nom'] ?? '');
    $prenom = trim($_POST['prenom'] ?? '');
    $description = trim($_POST['description'] ?? '');
    
    // Validation
    if (empty($nom) || empty($prenom)) {
        $error = "Le nom et prénom sont obligatoires.";
    } else {
        try {
            $stmt = $pdo->prepare("UPDATE utilisateur SET NOM = :nom, PRENOM = :prenom, DESCRIPTION = :description WHERE EMAIL = :email");
            $stmt->execute([
                ':nom' => $nom,
                ':prenom' => $prenom,
                ':description' => $description,
                ':email' => $_SESSION["email"]
            ]);
            
            $success = "Profil mis à jour avec succès!";
            
            // Refresh user data
            $stmt = $pdo->prepare("SELECT * FROM utilisateur WHERE EMAIL = :email");
            $stmt->execute([':email' => $_SESSION["email"]]);
            $user = $stmt->fetch();
            
        } catch (PDOException $e) {
            $error = "Erreur lors de la mise à jour du profil.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modifier mon profil | YADFYAD</title>
    <link rel="stylesheet" href="assets/styles/style.css">
    <link rel="stylesheet" href="authentification.css">
</head>
<body>
    <?php require_once "sections/navbar.php"; ?>
    
    <section class="connexion">
        <div class="container">
            <div class="info">
                <div class="connecte">
                    <div class="title">
                        <h2>Modifier mon profil</h2>
                        <p>Mettez à jour vos informations personnelles</p>
                    </div>
                    
                    <?php if ($error): ?>
                        <div class="form-error"><?= htmlspecialchars($error) ?></div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div style="color: green; margin: 10px 0;"><?= htmlspecialchars($success) ?></div>
                    <?php endif; ?>
                    
                    <form method="post" action="">
                        <label for="prenom">Prénom</label>
                        <input type="text" id="prenom" name="prenom" value="<?= htmlspecialchars($user['PRENOM']) ?>" required>

                        <label for="nom">Nom</label>
                        <input type="text" id="nom" name="nom" value="<?= htmlspecialchars($user['NOM']) ?>" required>

                        <label for="description">Description</label>
                        <textarea id="description" name="description" rows="4" style="width: 100%; padding: 12px; border: #71717a33 1px solid; border-radius: 8px; font-size: 16px; font-family: inherit; resize: vertical;"><?= htmlspecialchars($user['DESCRIPTION'] ?? '') ?></textarea>

                        <input type="submit" value="Mettre à jour">
                        
                        <div class="question">
                            <a href="profile_router.php">← Retour au profil</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
    
    <footer>
        <div class="copyright">
            &copy; 2025 YADFYAD. Tous droits réservés.
        </div>
    </footer>
</body>
</html>
