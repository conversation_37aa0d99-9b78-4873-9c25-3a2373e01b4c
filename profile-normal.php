<?php
session_start();
require_once "config.php";

// Check if user is logged in
if (!isset($_SESSION["email"])) {
    header("Location: connexion.php");
    exit;
}

// Get user information
$stmt = $pdo->prepare("SELECT * FROM utilisateur WHERE EMAIL = :email");
$stmt->execute([':email' => $_SESSION["email"]]);
$user = $stmt->fetch();

if (!$user) {
    header("Location: connexion.php");
    exit;
}

// Get association info if user is a member
$association = null;
if ($user['ID_ASSOCIATION']) {
    $stmt = $pdo->prepare("SELECT * FROM association WHERE ID_ASSOCIATION = :id");
    $stmt->execute([':id' => $user['ID_ASSOCIATION']]);
    $association = $stmt->fetch();
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><?= htmlspecialchars($user['PRENOM'] . ' ' . $user['NOM']) ?> | Profile</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap">
  <link rel="stylesheet" href="assets/styles/style.css">
  <link rel="stylesheet" href="profile-normal.css">
</head>

<body>
  <!-- NAVBARE -->
  <?php require_once "sections/navbar.php"; ?>

  <!-- PROFILE -->
  <section>

    <div class="container">
      <div class="head-profil">
        <div class="photo-profil">
          <div class="avatar"></div>
        </div>
        <div class="script-profil">
          <h2><?= htmlspecialchars($user['PRENOM'] . ' ' . $user['NOM']) ?></h2>
          <p><?= htmlspecialchars($user['DESCRIPTION'] ?? 'Membre de la communauté YADFYAD') ?></p>
          <?php if ($association): ?>
          <p><strong>Membre de:</strong> <?= htmlspecialchars($association['NOM_ASSOCIATION']) ?></p>
          <?php endif; ?>
          <div class="infos">
            <div class="lieu"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-map-pin-icon lucide-map-pin">
                <path
                  d="M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0" />
                <circle cx="12" cy="10" r="3" />
              </svg> <span><?= $association ? htmlspecialchars($association['ADRESSE'] ?? 'Maroc') : 'Maroc' ?></span> </div>
            <div class="modifier"><a href="profile-user-edit.php">Modifier</a></div>

          </div>
        </div>
      </div>
    </div>
  </section>
  <footer>
    <div class="copyright">
      &copy; 2025 YADFYAD.Tous droits reveser.
    </div>
  </footer>
</body>

</html>