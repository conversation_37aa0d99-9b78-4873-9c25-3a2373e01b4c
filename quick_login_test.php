<?php
/**
 * Quick login test to verify the actualite page works
 */
session_start();

// Simulate login with test user
$_SESSION['email'] = '<EMAIL>';
$_SESSION['type'] = 'utilisateur';

echo "<h1>Quick Login Test</h1>";
echo "<p>✅ Session set for: " . $_SESSION['email'] . "</p>";
echo "<p>✅ User type: " . $_SESSION['type'] . "</p>";
echo "<p><a href='actualite.php'>🔗 Go to Actualite Page</a></p>";
echo "<p><a href='logout.php'>🚪 Logout</a></p>";

// Test database connection and show some publications
require_once 'config.php';

try {
    $sql = $pdo->prepare("SELECT
        publication.ID_PUB,
        publication.TITRE,
        publication.DISCRIPTION,
        publication.TYPE_PUB,
        publication.DATE_CREATION,
        association.NOM_ASSOCIATION
        FROM publication
        INNER JOIN utilisateur ON publication.ID_UTILISATEUR = utilisateur.ID_UTILISATEUR
        LEFT JOIN association ON utilisateur.ID_ASSOCIATION = association.ID_ASSOCIATION
        ORDER BY publication.DATE_CREATION DESC
        LIMIT 3");
    $sql->execute();
    $publications = $sql->fetchAll();
    
    echo "<h2>Recent Publications Preview:</h2>";
    foreach ($publications as $pub) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h3>" . htmlspecialchars($pub['TITRE']) . "</h3>";
        echo "<p><strong>Type:</strong> " . $pub['TYPE_PUB'] . "</p>";
        echo "<p><strong>Association:</strong> " . ($pub['NOM_ASSOCIATION'] ?? 'N/A') . "</p>";
        echo "<p><strong>Date:</strong> " . date('d M Y, H:i', strtotime($pub['DATE_CREATION'])) . "</p>";
        echo "<p>" . substr(htmlspecialchars($pub['DISCRIPTION']), 0, 100) . "...</p>";
        echo "</div>";
    }
    
    if (empty($publications)) {
        echo "<p>⚠️ No publications found. You may need to add some sample data.</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error loading publications: " . $e->getMessage() . "</p>";
}
?>
