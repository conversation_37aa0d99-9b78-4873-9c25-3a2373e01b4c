<?php
/**
 * Profile Router - Routes users to appropriate profile page based on their role
 * 
 * This file determines which profile page to show based on:
 * - User type (association vs utilisateur)
 * - User permissions
 * - Whether viewing own profile or someone else's
 */

session_start();
require_once "config.php";

// Check if user is logged in
if (!isset($_SESSION["email"]) || !isset($_SESSION["type"])) {
    header("Location: connexion.php");
    exit;
}

$userEmail = $_SESSION["email"];
$userType = $_SESSION["type"];

// Check if viewing someone else's profile
$viewingProfileId = isset($_GET['id']) ? (int)$_GET['id'] : null;

try {
    // Get current user information
    if ($userType === 'association') {
        // User is an association
        $stmt = $pdo->prepare("SELECT * FROM association WHERE EMAIL = :email");
        $stmt->execute([':email' => $userEmail]);
        $currentUser = $stmt->fetch();
        $currentUserId = $currentUser['ID_ASSOCIATION'] ?? null;
        $isAssociation = true;
        
    } else {
        // User is a regular user (utilisateur)
        $stmt = $pdo->prepare("SELECT * FROM utilisateur WHERE EMAIL = :email");
        $stmt->execute([':email' => $userEmail]);
        $currentUser = $stmt->fetch();
        $currentUserId = $currentUser['ID_UTILISATEUR'] ?? null;
        $isAssociation = false;
        
        // Check if user is a member of an association
        $isAssociationMember = !empty($currentUser['ID_ASSOCIATION']);
    }
    
    // Determine which profile page to show
    if ($viewingProfileId) {
        // Viewing someone else's profile
        if ($viewingProfileId == $currentUserId && $isAssociation) {
            // Viewing own association profile
            header("Location: profile-association.php");
            exit;
        } else {
            // Viewing another association's profile
            header("Location: profile.php?id=" . $viewingProfileId);
            exit;
        }
    } else {
        // Viewing own profile
        if ($isAssociation) {
            // Association viewing their own profile
            header("Location: profile-association.php");
            exit;
            
        } elseif (isset($isAssociationMember) && $isAssociationMember) {
            // Association member viewing their profile
            header("Location: profile-association.php");
            exit;
            
        } else {
            // Regular user viewing their profile
            header("Location: profile-normal.php");
            exit;
        }
    }
    
} catch (PDOException $e) {
    // Database error - redirect to safe page
    error_log("Profile router database error: " . $e->getMessage());
    header("Location: actualite.php?error=profile_error");
    exit;
    
} catch (Exception $e) {
    // General error - redirect to safe page
    error_log("Profile router error: " . $e->getMessage());
    header("Location: actualite.php?error=profile_error");
    exit;
}

// Fallback - should never reach here
header("Location: actualite.php");
exit;
?>
