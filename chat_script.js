/**
 * Chat Frontend JavaScript - Sans framework
 * Gestion complète du chat en temps réel
 */

class ChatApp {
    constructor() {
        this.currentReceiverId = null;
        this.currentReceiverName = '';
        this.conversations = [];
        this.messages = [];
        this.pollInterval = null;
        this.lastMessageId = 0;

        this.init();
    }

    init() {
        this.bindEvents();
        this.loadConversations();
        this.startPolling();
        this.handleMobileNavigation();
    }

    bindEvents() {
        // Envoi de message avec le formulaire existant
        const chatForm = document.getElementById('chat-form');
        if (chatForm) {
            chatForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.sendMessage();
            });
        }

        // Recherche d'utilisateurs
        const searchInput = document.querySelector('.cherche input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchUsers(e.target.value);
            });
        }

        // Gestion du clavier
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                const messageInput = document.querySelector('.chat-input input[name="message"]');
                if (document.activeElement === messageInput) {
                    e.preventDefault();
                    this.sendMessage();
                }
            }
        });
    }


    async loadConversations() {
        try {
            const response = await fetch('chat_backend.php?action=get_conversations');
            const data = await response.json();

            if (data.success) {
                this.conversations = data.conversations;
                this.renderConversations();
            } else {
                this.showError('Erreur lors du chargement des conversations');
            }
        } catch (error) {
            this.showError('Erreur de connexion');
        }
    }

    renderConversations() {
        const conversationsList = document.querySelector('.chat-teams ul');
        if (!conversationsList) return;

        if (this.conversations.length === 0) {
            conversationsList.innerHTML = `
                <li class="no-conversations">
                    <div style="text-align: center; padding: 20px; color: #666;">
                        <p>Aucune conversation</p>
                        <small>Recherchez un utilisateur pour commencer</small>
                    </div>
                </li>
            `;
            return;
        }

        conversationsList.innerHTML = this.conversations.map(conv => `
            <li>
                <div class="contenu-message" onclick="chatApp.selectConversation(${conv.user_id}, '${conv.name}')">
                    <div class="profile">
                        <div class="avatare" style="background: linear-gradient(45deg, #667eea, #764ba2); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; width: 40px; height: 40px; border-radius: 50%;">
                            ${conv.name.charAt(0).toUpperCase()}
                        </div>
                    </div>
                    <div class="resume">
                        <div class="nom-user">${conv.name}</div>
                        <div class="result-message">${conv.last_message || 'Aucun message'}</div>
                    </div>
                    <div class="time-and-badge">
                        <div class="time">${conv.last_message_time}</div>
                        ${conv.unread_count > 0 ? `<div class="unread-badge" style="background: #e74c3c; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px;">${conv.unread_count}</div>` : ''}
                    </div>
                </div>
            </li>
        `).join('');
    }


    async selectConversation(userId, userName) {
        this.currentReceiverId = userId;
        this.currentReceiverName = userName;

        this.updateChatHeader(userName);
        await this.loadMessages();
        await this.markAsRead(userId);

        if (window.innerWidth <= 767) {
            this.showChatOnMobile();
        }
    }

    updateChatHeader(userName) {
        const chatHeader = document.querySelector('.chat-header');
        if (chatHeader) {
            chatHeader.innerHTML = `
                <div class="mobile-back-btn" onclick="chatApp.showSidebarOnMobile()" style="display: none; cursor: pointer; padding: 10px;">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="m12 19-7-7 7-7"/><path d="M19 12H5"/>
                    </svg>
                </div>
                <div class="profile">
                    <div class="avatare" style="background: linear-gradient(45deg, #667eea, #764ba2); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; width: 40px; height: 40px; border-radius: 50%;">
                        ${userName.charAt(0).toUpperCase()}
                    </div>
                </div>
                <div class="nom-user">${userName}</div>
            `;
        }
    }

    async loadMessages() {
        if (!this.currentReceiverId) return;

        try {
            const response = await fetch(`chat_backend.php?action=get_messages&receiver_id=${this.currentReceiverId}`);
            const data = await response.json();

            if (data.success) {
                this.messages = data.messages;
                this.renderMessages();
                this.scrollToBottom();

                if (this.messages.length > 0) {
                    this.lastMessageId = Math.max(...this.messages.map(m => m.id));
                }
            }
        } catch (error) {
            this.showError('Erreur lors du chargement des messages');
        }
    }

    renderMessages() {
        const messagesList = document.querySelector('.chat-messages ul');
        if (!messagesList) return;

        if (this.messages.length === 0) {
            messagesList.innerHTML = `
                <li class="no-messages" style="text-align: center; color: #666; padding: 20px;">
                    Aucun message. Commencez la conversation !
                </li>
            `;
            return;
        }

        messagesList.innerHTML = this.messages.map(msg => `
            <li class="message ${msg.is_sent ? 'sent' : 'received'}">
                <p>${this.escapeHtml(msg.message)}</p>
                <div class="time">${msg.time}</div>
            </li>
        `).join('');
    }

    async sendMessage() {
        const messageInput = document.querySelector('.chat-input input[name="message"]');
        if (!messageInput || !this.currentReceiverId) return;

        const message = messageInput.value.trim();
        if (!message) return;

        const sendButton = document.querySelector('.chat-input button');
        if (sendButton) {
            sendButton.disabled = true;
            sendButton.textContent = 'Envoi...';
        }

        try {
            const response = await fetch('chat_backend.php?action=send_message', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    receiver_id: this.currentReceiverId,
                    message: message,
                    type: 'text'
                })
            });

            const data = await response.json();

            if (data.success) {
                messageInput.value = '';
                await this.loadMessages();
                await this.loadConversations();
            } else {
                this.showError('Erreur lors de l\'envoi du message');
            }
        } catch (error) {
            this.showError('Erreur de connexion');
        } finally {
            if (sendButton) {
                sendButton.disabled = false;
                sendButton.textContent = 'Envoyer';
            }
        }
    }

    async markAsRead(senderId) {
        try {
            await fetch('chat_backend.php?action=mark_as_read', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ sender_id: senderId })
            });
        } catch (error) {
            console.error('Erreur lors du marquage comme lu:', error);
        }
    }

    async searchUsers(query) {
        if (query.length < 2) {
            this.loadConversations();
            return;
        }

        try {
            const response = await fetch(`chat_backend.php?action=search_users&q=${encodeURIComponent(query)}`);
            const data = await response.json();

            if (data.success) {
                this.renderSearchResults(data.users);
            }
        } catch (error) {
            console.error('Erreur de recherche:', error);
        }
    }

    renderSearchResults(users) {
        const conversationsList = document.querySelector('.chat-teams ul');
        if (!conversationsList) return;

        if (users.length === 0) {
            conversationsList.innerHTML = `
                <li class="no-results">
                    <div style="text-align: center; padding: 20px; color: #666;">
                        Aucun utilisateur trouvé
                    </div>
                </li>
            `;
            return;
        }

        conversationsList.innerHTML = users.map(user => `
            <li>
                <div class="contenu-message" onclick="chatApp.selectConversation(${user.id}, '${user.name}')">
                    <div class="profile">
                        <div class="avatare" style="background: linear-gradient(45deg, #667eea, #764ba2); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; width: 40px; height: 40px; border-radius: 50%;">
                            ${user.name.charAt(0).toUpperCase()}
                        </div>
                    </div>
                    <div class="resume">
                        <div class="nom-user">${user.name}</div>
                        <div class="result-message">${user.email}</div>
                    </div>
                    <div class="time">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 6L9 17l-5-5"/>
                        </svg>
                    </div>
                </div>
            </li>
        `).join('');
    }

    startPolling() {
        this.pollInterval = setInterval(() => {
            if (this.currentReceiverId) {
                this.checkNewMessages();
            }
            this.loadConversations();
        }, 3000);
    }

    async checkNewMessages() {
        try {
            const response = await fetch(`chat_backend.php?action=get_messages&receiver_id=${this.currentReceiverId}`);
            const data = await response.json();

            if (data.success && data.messages.length > 0) {
                const newLastMessageId = Math.max(...data.messages.map(m => m.id));

                if (newLastMessageId > this.lastMessageId) {
                    this.messages = data.messages;
                    this.renderMessages();
                    this.scrollToBottom();
                    this.lastMessageId = newLastMessageId;
                }
            }
        } catch (error) {
            console.error('Erreur lors de la vérification des nouveaux messages:', error);
        }
    }

    scrollToBottom() {
        const messagesContainer = document.querySelector('.chat-messages');
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'chat-error';
        errorDiv.style.cssText = `
            position: fixed; top: 20px; right: 20px; background: #f44336; color: white;
            padding: 15px; border-radius: 5px; z-index: 1000; font-size: 14px;
        `;
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);
        setTimeout(() => errorDiv.remove(), 3000);
    }

    handleMobileNavigation() {
        if (window.innerWidth <= 767) {
            this.setupMobileView();
        }

        window.addEventListener('resize', () => {
            if (window.innerWidth <= 767) {
                this.setupMobileView();
            } else {
                this.setupDesktopView();
            }
        });
    }

    setupMobileView() {
        const backBtn = document.querySelector('.mobile-back-btn');
        if (backBtn) backBtn.style.display = 'block';
    }

    setupDesktopView() {
        const backBtn = document.querySelector('.mobile-back-btn');
        if (backBtn) backBtn.style.display = 'none';

        const parentContainer = document.querySelector('.parent-container');
        if (parentContainer) parentContainer.classList.remove('chat-active');
    }

    showChatOnMobile() {
        const parentContainer = document.querySelector('.parent-container');
        if (parentContainer) parentContainer.classList.add('chat-active');
    }

    showSidebarOnMobile() {
        const parentContainer = document.querySelector('.parent-container');
        if (parentContainer) parentContainer.classList.remove('chat-active');
    }

    destroy() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
        }
    }
}

// Initialisation
let chatApp;
document.addEventListener('DOMContentLoaded', () => {
    chatApp = new ChatApp();
});

window.addEventListener('beforeunload', () => {
    if (chatApp) chatApp.destroy();
});
