const chatForm = document.getElementById('chat-form');
const chatMessages = document.querySelector('.chat-messages ul');

chatForm.addEventListener('submit', function(e) {
  e.preventDefault();
  const input = chatForm.message;
  if (input.value.trim() === '') return;
  
  // Création du message envoyé
  const li = document.createElement('li');
  li.classList.add('message', 'sent');
  li.innerHTML = `<p>${input.value.trim()}</p><div class="time">${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>`;
  
  chatMessages.appendChild(li);
  input.value = '';
  chatMessages.scrollTop = chatMessages.scrollHeight; // Scroll bas
});

// Simuler une réponse du bot
document.addEventListener('DOMContentLoaded', () => {
  const input = document.querySelector('.chat-input input'); // champ de saisie du message
  const sendBtn = document.querySelector('.chat-input button'); // bouton envoyer
  const messagesList = document.querySelector('.chat-messages ul'); // liste des messages affichés

  // ID du destinataire (à remplacer dynamiquement selon l'association sélectionnée)
  const receiverId = 2;

  // Fonction pour ajouter un message dans l'interface
  // sent = true si le message est envoyé par l'utilisateur, sinon reçu
  function addMessage(text, sent = true) {
    const li = document.createElement('li');
    li.classList.add('message');
    li.classList.add(sent ? 'sent' : 'received');
    li.innerHTML = `
      <p>${text}</p>
      <div class="time">${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
    `;
    messagesList.appendChild(li);
    // Scroll vers le bas pour voir le dernier message
    messagesList.scrollTop = messagesList.scrollHeight;
  }

  // Gestion de l'envoi du message lors du clic sur le bouton
  sendBtn.addEventListener('click', () => {
    const message = input.value.trim();
    if (message === '') return; // ne rien faire si champ vide

    // Envoi de la requête POST au serveur via fetch API
    fetch('send_message.php', {
      method: 'POST',
      headers: {'Content-Type': 'application/x-www-form-urlencoded'},
      body: `receiver_id=${receiverId}&message=${encodeURIComponent(message)}`
    })
    .then(res => res.json())
    .then(data => {
      if (data.status === 'success') {
        // Ajouter le message envoyé dans l'affichage
        addMessage(message, true);
        // Vider le champ de saisie
        input.value = '';
      } else {
        alert('Erreur : ' + data.message);
      }
    });
  });

  // Fonction pour charger les messages depuis le serveur
  function loadMessages() {
    fetch(`get_messages.php?receiver_id=${receiverId}`)
      .then(res => res.json())
      .then(messages => {
        // Vider la liste avant de recharger
        messagesList.innerHTML = '';
        // Ajouter chaque message dans la liste
        messages.forEach(msg => {
          // Si sender_id différent de receiverId, message reçu sinon envoyé
          addMessage(msg.message, msg.sender_id !== receiverId);
        });
      });
  }

  // Chargement initial des messages
  loadMessages();

  // Actualiser les messages toutes les 5 secondes pour simuler un chat en temps réel
  setInterval(loadMessages, 5000);
});
