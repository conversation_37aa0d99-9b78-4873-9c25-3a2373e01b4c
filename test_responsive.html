<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Responsive Design | YADFYAD</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 40px 20px;
            margin-bottom: 30px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .test-card:hover {
            transform: translateY(-5px);
        }
        
        .test-card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .test-card p {
            color: #666;
            margin-bottom: 15px;
        }
        
        .test-link {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 6px;
            transition: background 0.3s ease;
        }
        
        .test-link:hover {
            background: #5a67d8;
        }
        
        .responsive-info {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .breakpoint-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .breakpoint {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .current-viewport {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .features-list {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .features-list ul {
            list-style: none;
            padding: 0;
        }
        
        .features-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 25px;
        }
        
        .features-list li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        
        .features-list li:last-child {
            border-bottom: none;
        }
        
        @media (max-width: 767px) {
            .container {
                padding: 15px;
            }
            
            .header {
                padding: 30px 15px;
            }
            
            .test-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .test-card {
                padding: 20px;
            }
            
            .breakpoint-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 Test Responsive Design YADFYAD</h1>
        <p>Testez toutes les pages sur différentes tailles d'écran</p>
    </div>
    
    <div class="container">
        <div class="current-viewport">
            <h3>📱 Taille d'écran actuelle</h3>
            <p id="viewport-size">Chargement...</p>
        </div>
        
        <div class="responsive-info">
            <h2>📐 Points de rupture (Breakpoints)</h2>
            <p>Le design responsive utilise ces points de rupture pour s'adapter aux différents appareils :</p>
            
            <div class="breakpoint-info">
                <div class="breakpoint">
                    <h4>📱 Mobile</h4>
                    <p><strong>320px - 767px</strong></p>
                    <p>Navigation mobile, colonnes simples, texte optimisé</p>
                </div>
                <div class="breakpoint">
                    <h4>📱 Tablette</h4>
                    <p><strong>768px - 1023px</strong></p>
                    <p>Mise en page hybride, grilles adaptées</p>
                </div>
                <div class="breakpoint">
                    <h4>🖥️ Desktop</h4>
                    <p><strong>1024px+</strong></p>
                    <p>Mise en page complète, toutes les fonctionnalités</p>
                </div>
            </div>
        </div>
        
        <h2>🧪 Pages à tester</h2>
        <div class="test-grid">
            <div class="test-card">
                <h3>🏠 Page d'accueil</h3>
                <p>Hero section responsive, grilles adaptatives, navigation mobile</p>
                <a href="index.php" class="test-link" target="_blank">Tester</a>
            </div>
            
            <div class="test-card">
                <h3>🔐 Connexion</h3>
                <p>Formulaires optimisés mobile, mise en page adaptative</p>
                <a href="connexion.php" class="test-link" target="_blank">Tester</a>
            </div>
            
            <div class="test-card">
                <h3>📰 Actualités</h3>
                <p>Feed responsive, cartes adaptatives, navigation mobile</p>
                <a href="quick_login_test.php" class="test-link" target="_blank">Tester (avec login)</a>
            </div>
            
            <div class="test-card">
                <h3>👤 Profil Utilisateur</h3>
                <p>Profil responsive, boutons adaptatifs, images optimisées</p>
                <a href="profile-normal.php" class="test-link" target="_blank">Tester</a>
            </div>
            
            <div class="test-card">
                <h3>🏢 Profil Association</h3>
                <p>Profil d'association responsive, onglets adaptatifs</p>
                <a href="profile-association.php" class="test-link" target="_blank">Tester</a>
            </div>
            
            <div class="test-card">
                <h3>💬 Chat</h3>
                <p>Interface de chat mobile-first, sidebar adaptative</p>
                <a href="chat.php" class="test-link" target="_blank">Tester</a>
            </div>
            
            <div class="test-card">
                <h3>📝 Inscription</h3>
                <p>Formulaires longs optimisés mobile, validation adaptée</p>
                <a href="inscription.php" class="test-link" target="_blank">Tester</a>
            </div>
            
            <div class="test-card">
                <h3>⚙️ Admin Panel</h3>
                <p>Interface d'administration responsive, tableaux adaptatifs</p>
                <a href="admin/index.php" class="test-link" target="_blank">Tester</a>
            </div>

            <div class="test-card">
                <h3>📝 Publications</h3>
                <p>Formulaires de publication responsive (activité, expérience, problème)</p>
                <a href="test_publications_responsive.html" class="test-link" target="_blank">Tester Publications</a>
            </div>
        </div>
        
        <div class="features-list">
            <h2>✨ Fonctionnalités Responsive Implémentées</h2>
            <ul>
                <li>Navigation mobile avec menu hamburger animé</li>
                <li>Grilles CSS adaptatives pour tous les contenus</li>
                <li>Images responsive avec lazy loading</li>
                <li>Formulaires optimisés pour mobile (pas de zoom iOS)</li>
                <li>Interactions tactiles améliorées</li>
                <li>Gestes de balayage pour la navigation mobile</li>
                <li>Typographie responsive avec tailles adaptatives</li>
                <li>Boutons et liens optimisés pour le tactile</li>
                <li>Chat interface adaptée mobile/desktop</li>
                <li>Tableaux d'administration avec scroll horizontal</li>
                <li>Cartes de contenu empilables sur mobile</li>
                <li>Footer responsive avec colonnes adaptatives</li>
                <li>Gestion automatique de l'orientation d'écran</li>
                <li>Performance optimisée pour les appareils mobiles</li>
            </ul>
        </div>
    </div>
    
    <script>
        // Afficher la taille de l'écran en temps réel
        function updateViewportSize() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            let deviceType = '';
            
            if (width <= 767) {
                deviceType = '📱 Mobile';
            } else if (width <= 1023) {
                deviceType = '📱 Tablette';
            } else {
                deviceType = '🖥️ Desktop';
            }
            
            document.getElementById('viewport-size').innerHTML = 
                `<strong>${width} × ${height}px</strong><br>${deviceType}`;
        }
        
        // Mettre à jour au chargement et au redimensionnement
        updateViewportSize();
        window.addEventListener('resize', updateViewportSize);
        
        // Ajouter des effets tactiles sur mobile
        if ('ontouchstart' in window) {
            document.querySelectorAll('.test-card, .test-link').forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                });
                
                element.addEventListener('touchend', function() {
                    this.style.transform = '';
                });
            });
        }
    </script>
</body>
</html>
