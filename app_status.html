<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YADFYAD - App Status</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 90%;
            text-align: center;
        }
        
        .status-badge {
            background: #10B981;
            color: white;
            padding: 8px 20px;
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 30px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            background: #f8fafc;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }
        
        .feature h3 {
            color: #333;
            margin-bottom: 8px;
        }
        
        .feature p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }
        
        .btn-secondary:hover {
            background: #cbd5e0;
        }
        
        .credentials {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin: 30px 0;
            text-align: left;
        }
        
        .credentials h3 {
            color: #2d3748;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .cred-item {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 6px;
        }
        
        .cred-item strong {
            color: #4a5568;
        }
        
        .cred-item code {
            background: #edf2f7;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9rem;
        }
        
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="status-badge">🟢 FULLY OPERATIONAL</div>
        
        <h1>YADFYAD</h1>
        <p class="subtitle">Platform for Solidarity Associations - Ready to Use!</p>
        
        <div class="features">
            <div class="feature">
                <h3>✅ Database Setup</h3>
                <p>MySQL database created with all tables and sample data</p>
            </div>
            <div class="feature">
                <h3>✅ User System</h3>
                <p>Registration, login, and profile management working</p>
            </div>
            <div class="feature">
                <h3>✅ Publications</h3>
                <p>Share problems, activities, and experiences</p>
            </div>
            <div class="feature">
                <h3>✅ Social Features</h3>
                <p>Like, follow, and chat functionality enabled</p>
            </div>
        </div>
        
        <div class="credentials">
            <h3>🔐 Test Credentials</h3>
            <div class="cred-item">
                <strong>Association:</strong><br>
                Email: <code><EMAIL></code><br>
                Password: <code>password</code>
            </div>
            <div class="cred-item">
                <strong>User:</strong><br>
                Email: <code><EMAIL></code><br>
                Password: <code>password</code>
            </div>
            <div class="cred-item">
                <strong>Admin:</strong><br>
                Email: <code><EMAIL></code><br>
                Password: <code>password</code>
            </div>
        </div>
        
        <div class="actions">
            <a href="index.php" class="btn btn-primary">🏠 Visit Homepage</a>
            <a href="connexion.php" class="btn btn-primary">🔐 Login</a>
            <a href="inscription.php" class="btn btn-secondary">📝 Register Association</a>
            <a href="test_app.php" class="btn btn-secondary">🔧 System Test</a>
        </div>
        
        <div class="footer">
            <p><strong>All systems operational!</strong> The app is ready for use with no frameworks - pure PHP, HTML, CSS, and JavaScript.</p>
        </div>
    </div>
</body>
</html>
