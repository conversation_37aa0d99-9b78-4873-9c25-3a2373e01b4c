<?php
/**
 * Database Update Script for YADFYAD
 * This script adds missing columns to existing database
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

try {
    echo "Starting database update...\n";
    
    // Check if DATE_EVENEMENT_ACTIVITE column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM publication LIKE 'DATE_EVENEMENT_ACTIVITE'");
    $columnExists = $stmt->rowCount() > 0;
    
    if (!$columnExists) {
        echo "Adding DATE_EVENEMENT_ACTIVITE column to publication table...\n";
        $pdo->exec("ALTER TABLE publication ADD COLUMN DATE_EVENEMENT_ACTIVITE DATE AFTER LIEU_EVENEMENT_LACTIVITE");
        echo "✅ Column added successfully!\n";
    } else {
        echo "✅ DATE_EVENEMENT_ACTIVITE column already exists.\n";
    }
    
    echo "\n=== Database Update Complete ===\n";
    echo "All necessary columns have been added.\n";
    
} catch (PDOException $e) {
    echo "❌ Database update failed: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
