<?php
/**
 * Database Update Script for YADFYAD
 * This script adds missing columns to existing database
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

try {
    echo "Starting database update...\n";

    // Check current publication table structure
    echo "Checking publication table structure...\n";
    $stmt = $pdo->query("SHOW COLUMNS FROM publication");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Current columns: " . implode(', ', $columns) . "\n\n";

    // Check if DATE_EVENEMENT_ACTIVITE column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM publication LIKE 'DATE_EVENEMENT_ACTIVITE'");
    $dateEventColumnExists = $stmt->rowCount() > 0;

    if (!$dateEventColumnExists) {
        echo "Adding DATE_EVENEMENT_ACTIVITE column to publication table...\n";
        $pdo->exec("ALTER TABLE publication ADD COLUMN DATE_EVENEMENT_ACTIVITE DATE AFTER LIEU_EVENEMENT_LACTIVITE");
        echo "✅ DATE_EVENEMENT_ACTIVITE column added successfully!\n";
    } else {
        echo "✅ DATE_EVENEMENT_ACTIVITE column already exists.\n";
    }

    // Check if date_modification column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM publication LIKE 'date_modification'");
    $dateModificationExists = $stmt->rowCount() > 0;

    if (!$dateModificationExists) {
        echo "Adding date_modification column to publication table...\n";
        $pdo->exec("ALTER TABLE publication ADD COLUMN date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
        echo "✅ date_modification column added successfully!\n";
    } else {
        echo "✅ date_modification column already exists.\n";
    }

    echo "\n=== Database Update Complete ===\n";
    echo "All necessary columns have been added.\n";
    
} catch (PDOException $e) {
    echo "❌ Database update failed: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
