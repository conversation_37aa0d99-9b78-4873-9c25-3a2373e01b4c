<?php
/**
 * API pour récupérer les notifications
 */

session_start();
require_once 'config.php';

header('Content-Type: application/json');

// Vérification de l'authentification
if (!isset($_SESSION['email'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Non authentifié']);
    exit;
}

// Récupération de l'utilisateur
try {
    $stmt = $pdo->prepare("SELECT ID_UTILISATEUR FROM utilisateur WHERE EMAIL = :email");
    $stmt->execute([':email' => $_SESSION['email']]);
    $user = $stmt->fetch();
    
    if (!$user) {
        http_response_code(401);
        echo json_encode(['success' => false, 'error' => 'Utilisateur non trouvé']);
        exit;
    }
    
    $userId = $user['ID_UTILISATEUR'];
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Erreur de base de données']);
    exit;
}

// Créer la table des notifications si elle n'existe pas
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            type VARCHAR(50) NOT NULL,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            data JSON,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES utilisateur(ID_UTILISATEUR) ON DELETE CASCADE,
            INDEX idx_user_created (user_id, created_at),
            INDEX idx_user_read (user_id, is_read)
        )
    ");
} catch (PDOException $e) {
    // Table existe déjà
}

try {
    // Récupération des notifications récentes
    $stmt = $pdo->prepare("
        SELECT * FROM notifications 
        WHERE user_id = :user_id 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stmt->execute([':user_id' => $userId]);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Compter les notifications non lues
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as unread_count 
        FROM notifications 
        WHERE user_id = :user_id AND is_read = FALSE
    ");
    $stmt->execute([':user_id' => $userId]);
    $unreadCount = $stmt->fetch()['unread_count'];
    
    // Si aucune notification, créer des exemples
    if (empty($notifications)) {
        $sampleNotifications = [
            [
                'type' => 'welcome',
                'title' => 'Bienvenue sur YADFYAD !',
                'message' => 'Merci de rejoindre notre communauté d\'associations.',
                'data' => json_encode(['action' => 'explore'])
            ],
            [
                'type' => 'like',
                'title' => 'Nouvelle interaction',
                'message' => 'Votre publication a reçu de nouveaux likes.',
                'data' => json_encode(['post_id' => 1, 'likes_count' => 5])
            ]
        ];
        
        foreach ($sampleNotifications as $notification) {
            $stmt = $pdo->prepare("
                INSERT INTO notifications (user_id, type, title, message, data) 
                VALUES (:user_id, :type, :title, :message, :data)
            ");
            $stmt->execute([
                ':user_id' => $userId,
                ':type' => $notification['type'],
                ':title' => $notification['title'],
                ':message' => $notification['message'],
                ':data' => $notification['data']
            ]);
        }
        
        // Recharger les notifications
        $stmt = $pdo->prepare("
            SELECT * FROM notifications 
            WHERE user_id = :user_id 
            ORDER BY created_at DESC 
            LIMIT 10
        ");
        $stmt->execute([':user_id' => $userId]);
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $unreadCount = count($notifications);
    }
    
    // Formatage des notifications
    $formattedNotifications = array_map(function($notification) {
        return [
            'id' => $notification['id'],
            'type' => $notification['type'],
            'title' => $notification['title'],
            'message' => $notification['message'],
            'data' => json_decode($notification['data'], true),
            'is_read' => (bool)$notification['is_read'],
            'created_at' => $notification['created_at'],
            'time_ago' => timeAgo($notification['created_at'])
        ];
    }, $notifications);
    
    echo json_encode([
        'success' => true,
        'notifications' => $formattedNotifications,
        'unread_count' => (int)$unreadCount
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Erreur lors de la récupération des notifications']);
}

/**
 * Calcule le temps écoulé depuis une date
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return 'À l\'instant';
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return "Il y a {$minutes} min";
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return "Il y a {$hours}h";
    } elseif ($time < 2592000) {
        $days = floor($time / 86400);
        return "Il y a {$days}j";
    } else {
        return date('d/m/Y', strtotime($datetime));
    }
}
?>
