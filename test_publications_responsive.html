<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Publications Responsive | YADFYAD</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #16a085 0%, #27ae60 100%);
            color: white;
            text-align: center;
            padding: 40px 20px;
            margin-bottom: 30px;
            border-radius: 12px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            border-left: 4px solid;
        }
        
        .test-card.activite {
            border-left-color: #16a085;
        }
        
        .test-card.experience {
            border-left-color: #9333EA;
        }
        
        .test-card.probleme {
            border-left-color: #e74c3c;
        }
        
        .test-card:hover {
            transform: translateY(-5px);
        }
        
        .test-card h3 {
            margin-bottom: 15px;
            font-size: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .test-card.activite h3 {
            color: #16a085;
        }
        
        .test-card.experience h3 {
            color: #9333EA;
        }
        
        .test-card.probleme h3 {
            color: #e74c3c;
        }
        
        .test-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .test-link {
            display: inline-block;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            color: white;
        }
        
        .test-card.activite .test-link {
            background: #16a085;
        }
        
        .test-card.experience .test-link {
            background: #9333EA;
        }
        
        .test-card.probleme .test-link {
            background: #e74c3c;
        }
        
        .test-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .features-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .feature-item h4 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .current-viewport {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .responsive-tips {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .responsive-tips h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .responsive-tips ul {
            color: #856404;
            padding-left: 20px;
        }
        
        .responsive-tips li {
            margin-bottom: 8px;
        }
        
        @media (max-width: 767px) {
            .container {
                padding: 15px;
            }
            
            .header {
                padding: 30px 15px;
            }
            
            .test-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .test-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📝 Test Publications Responsive</h1>
        <p>Testez les formulaires de publication sur tous les appareils</p>
    </div>
    
    <div class="container">
        <div class="current-viewport">
            <h3>📱 Taille d'écran actuelle</h3>
            <p id="viewport-size">Chargement...</p>
        </div>
        
        <div class="responsive-tips">
            <h3>💡 Points à tester sur mobile</h3>
            <ul>
                <li><strong>Navigation :</strong> Menu hamburger et retour facile</li>
                <li><strong>Formulaires :</strong> Champs pleine largeur, pas de zoom iOS</li>
                <li><strong>Boutons :</strong> Taille tactile optimale (44px minimum)</li>
                <li><strong>Upload :</strong> Interface de sélection de fichiers mobile</li>
                <li><strong>Validation :</strong> Messages d'erreur visibles</li>
                <li><strong>Soumission :</strong> Boutons pleine largeur sur mobile</li>
            </ul>
        </div>
        
        <h2>🧪 Pages de publication à tester</h2>
        <div class="test-grid">
            <div class="test-card activite">
                <h3>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M8 2v4"/>
                        <path d="M16 2v4"/>
                        <rect width="18" height="18" x="3" y="4" rx="2"/>
                        <path d="M3 10h18"/>
                    </svg>
                    Publier une Activité
                </h3>
                <p>Formulaire pour partager les activités réalisées par votre association. Inclut titre, date, lieu, description et upload d'image.</p>
                <a href="publication/activite.php" class="test-link" target="_blank">Tester Activité</a>
            </div>
            
            <div class="test-card experience">
                <h3>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 12l2 2 4-4"/>
                        <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
                        <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
                        <path d="M3 12h6m6 0h6"/>
                    </svg>
                    Partager une Expérience
                </h3>
                <p>Formulaire pour partager vos expériences et leçons apprises. Inclut titre, lieu, description détaillée, tags et upload d'image.</p>
                <a href="publication/experience.php" class="test-link" target="_blank">Tester Expérience</a>
            </div>
            
            <div class="test-card probleme">
                <h3>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <line x1="12" x2="12" y1="8" y2="12"/>
                        <line x1="12" x2="12.01" y1="16" y2="16"/>
                    </svg>
                    Signaler un Problème
                </h3>
                <p>Formulaire pour demander de l'aide à la communauté. Inclut titre, lieu, description du problème et catégories.</p>
                <a href="publication/probleme.php" class="test-link" target="_blank">Tester Problème</a>
            </div>
        </div>
        
        <div class="features-section">
            <h2>✨ Améliorations Responsive Implémentées</h2>
            <div class="features-grid">
                <div class="feature-item">
                    <h4>📱 Mobile-First Design</h4>
                    <p>Formulaires optimisés pour mobile avec champs pleine largeur et boutons tactiles.</p>
                </div>
                
                <div class="feature-item">
                    <h4>🎯 Champs Optimisés</h4>
                    <p>Types d'input appropriés (date, text, textarea) avec placeholders informatifs.</p>
                </div>
                
                <div class="feature-item">
                    <h4>📤 Upload Mobile</h4>
                    <p>Interface d'upload de fichiers adaptée aux appareils tactiles.</p>
                </div>
                
                <div class="feature-item">
                    <h4>🔤 Prévention Zoom iOS</h4>
                    <p>Font-size 16px minimum pour éviter le zoom automatique sur iOS.</p>
                </div>
                
                <div class="feature-item">
                    <h4>🎨 Design Adaptatif</h4>
                    <p>Mise en page qui s'adapte automatiquement à toutes les tailles d'écran.</p>
                </div>
                
                <div class="feature-item">
                    <h4>⚡ Performance</h4>
                    <p>CSS optimisé et JavaScript léger pour une expérience fluide.</p>
                </div>
            </div>
        </div>
        
        <div class="features-section">
            <h2>📋 Checklist de Test</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h3>📱 Mobile (< 768px)</h3>
                    <ul style="list-style: none; padding: 0;">
                        <li>☐ Navigation hamburger fonctionne</li>
                        <li>☐ Formulaires pleine largeur</li>
                        <li>☐ Boutons tactiles (44px min)</li>
                        <li>☐ Pas de zoom sur focus input</li>
                        <li>☐ Upload de fichiers fonctionnel</li>
                        <li>☐ Validation visible</li>
                    </ul>
                </div>
                
                <div>
                    <h3>📱 Tablette (768px - 1023px)</h3>
                    <ul style="list-style: none; padding: 0;">
                        <li>☐ Layout hybride adapté</li>
                        <li>☐ Formulaires bien proportionnés</li>
                        <li>☐ Navigation accessible</li>
                        <li>☐ Interactions tactiles fluides</li>
                    </ul>
                </div>
                
                <div>
                    <h3>🖥️ Desktop (> 1024px)</h3>
                    <ul style="list-style: none; padding: 0;">
                        <li>☐ Layout complet affiché</li>
                        <li>☐ Hover effects fonctionnels</li>
                        <li>☐ Navigation complète</li>
                        <li>☐ Formulaires bien centrés</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Afficher la taille de l'écran en temps réel
        function updateViewportSize() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            let deviceType = '';
            
            if (width <= 767) {
                deviceType = '📱 Mobile';
            } else if (width <= 1023) {
                deviceType = '📱 Tablette';
            } else {
                deviceType = '🖥️ Desktop';
            }
            
            document.getElementById('viewport-size').innerHTML = 
                `<strong>${width} × ${height}px</strong><br>${deviceType}`;
        }
        
        // Mettre à jour au chargement et au redimensionnement
        updateViewportSize();
        window.addEventListener('resize', updateViewportSize);
        
        // Ajouter des effets tactiles sur mobile
        if ('ontouchstart' in window) {
            document.querySelectorAll('.test-card, .test-link').forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                });
                
                element.addEventListener('touchend', function() {
                    this.style.transform = '';
                });
            });
        }
        
        // Cocher automatiquement les éléments de la checklist quand on clique dessus
        document.querySelectorAll('li').forEach(item => {
            if (item.textContent.includes('☐')) {
                item.style.cursor = 'pointer';
                item.addEventListener('click', function() {
                    if (this.textContent.includes('☐')) {
                        this.innerHTML = this.innerHTML.replace('☐', '✅');
                        this.style.color = '#28a745';
                    } else if (this.textContent.includes('✅')) {
                        this.innerHTML = this.innerHTML.replace('✅', '☐');
                        this.style.color = '';
                    }
                });
            }
        });
    </script>
</body>
</html>
