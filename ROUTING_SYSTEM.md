# YADFYAD Routing System Documentation

## 🎯 Overview
The YADFYAD platform now includes a comprehensive role-based routing system that automatically directs users to appropriate pages based on their roles and permissions.

## 👥 User Types & Roles

### 1. **Association** (`$_SESSION['type'] = 'association'`)
- **Verified Association**: Full access to association features
- **Pending Association**: Limited access until verification
- **Admin Association**: Special admin account with elevated privileges

### 2. **User** (`$_SESSION['type'] = 'utilisateur'`)
- **Regular User**: Standard member access
- **Association Member**: User linked to an association
- **Admin User**: User with admin privileges (`TYPE_UTILISATEUR = 1`)

## 🔄 Routing Flow

### Login Process (`connexion.php`)
```
Login → dashboard.php → Role-based redirect
```

### Dashboard Routing (`dashboard.php`)
```
Association (Verified) → profile-association.php
Association (Pending) → association_pending.php
Association (Admin) → admin/index.php
User (Admin) → admin/index.php
User (Regular) → actualite.php
```

### Profile Routing (`profile_router.php`)
```
Own Profile:
- Association → profile-association.php
- Association Member → profile-association.php
- Regular User → profile-normal.php

Other's Profile:
- Any User → profile.php?id={association_id}
```

## 📁 File Structure

### Core Routing Files
- `dashboard.php` - Main role-based dashboard router
- `profile_router.php` - Profile page router
- `association_pending.php` - Pending verification page

### Profile Pages
- `profile-association.php` - Association/member profile (dynamic)
- `profile-normal.php` - Regular user profile (dynamic)
- `profile.php` - View other profiles (public view)
- `profile-user-edit.php` - Edit user profile

### Admin Pages
- `admin/index.php` - Admin dashboard
- `admin/associations.php` - Manage associations
- `admin/utilisateurs.php` - Manage users
- `admin/verifications.php` - Verify associations

## 🔐 Access Control Matrix

| User Type | Own Profile | Other Profiles | Admin Panel | Create Posts | Verify Associations |
|-----------|-------------|----------------|-------------|--------------|-------------------|
| Association (Verified) | ✅ | ✅ | ❌ | ✅ | ❌ |
| Association (Pending) | ⏳ | ❌ | ❌ | ❌ | ❌ |
| Association Member | ✅ | ✅ | ❌ | ✅ | ❌ |
| Regular User | ✅ | ✅ | ❌ | ❌ | ❌ |
| Admin User | ✅ | ✅ | ✅ | ✅ | ✅ |
| Admin Association | ✅ | ✅ | ✅ | ✅ | ✅ |

## 🚀 Navigation Updates

### Navbar Links
- **Profile Icon** → `profile_router.php` (smart routing)
- **Home** → `actualite.php`
- **Associations** → `associations.php`
- **Chat** → `chat.php`
- **Logout** → `logout.php`

### Smart Redirects
- Login success → `dashboard.php`
- Profile click → `profile_router.php`
- Admin access → `admin/index.php`
- Pending verification → `association_pending.php`

## 🔧 Implementation Details

### Session Variables
```php
$_SESSION['email']    // User email
$_SESSION['type']     // 'association' or 'utilisateur'
```

### Database Checks
```php
// Association verification
$association['VERIFIE'] // true/false

// User admin status
$user['TYPE_UTILISATEUR'] // 0=regular, 1=admin

// Association member
$user['ID_ASSOCIATION'] // null or association ID
```

### Error Handling
- Invalid sessions → Redirect to login
- Database errors → Log and redirect safely
- Missing permissions → Redirect to appropriate page

## 🧪 Testing Scenarios

### Test Accounts
1. **Verified Association**: `<EMAIL>` / `password`
2. **Regular User**: `<EMAIL>` / `password`
3. **Admin**: `<EMAIL>` / `password`

### Test Cases
1. ✅ Login as association → Should go to association profile
2. ✅ Login as user → Should go to news feed
3. ✅ Login as admin → Should go to admin panel
4. ✅ Click profile icon → Should route to correct profile page
5. ✅ Unverified association → Should see pending page

## 🎯 Benefits

1. **User Experience**: Automatic routing to relevant content
2. **Security**: Role-based access control
3. **Maintainability**: Centralized routing logic
4. **Scalability**: Easy to add new roles and permissions
5. **Consistency**: Uniform navigation experience

## 🔄 Future Enhancements

- [ ] Role-based menu customization
- [ ] Permission-based feature toggles
- [ ] Advanced user role hierarchy
- [ ] Custom dashboard layouts per role
- [ ] Activity-based routing suggestions

---

**Status**: ✅ Fully implemented and tested
**Last Updated**: Current implementation
**Compatibility**: All user types and roles supported
