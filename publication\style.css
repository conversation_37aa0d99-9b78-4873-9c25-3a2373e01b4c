* {
   -webkit-box-sizing: border-box;
   -moz-box-sizing: border-box;
   box-sizing: border-box;
}

body {
   margin: 0;
   padding: 0;
   text-decoration: none;
   list-style: none;
   font-family: 'Inter', sans-serif;

}

.container {
   width: 100%;
   max-width: 1200px;
   margin-left: auto;
   margin-right: auto;
   padding: 0 20px;
}

:root {
   --mainColor: red;
   --lightgreyColor: #bcbcbc;
}

section {
   width: 100%;
   min-height: 100vh;
   position: relative;
   padding: 20px 0;
}

.direction {
   display: flex;
   justify-content: left;
   align-items: center;
   margin: 20px;
}

.direction span {
   font-weight: 200;
   color: #6e6c6c;
   font-size: 16px;
   margin-left: 10px;
}

.titre {
   width: 100%;
   display: flex;
   flex-direction: column;
   align-items: center;

}

.titre .icone {
   width: 50px;
   height: 50px;
   border-radius: 50%;
   background-color: rgba(255, 0, 0, 0.164);
   display: flex;
   justify-content: center;
   align-items: center;
}

.titre h2 {
   font-size: 29px;
   font-weight: bolder;
   margin: 12px;

}

.titre p {
   font-size: 19px;
   color: #6e6c6c66;
   font-weight: 500;
   margin-top: 0;

}

.box {
   display: flex;
   flex-direction: column;
   width: 100%;
   max-width: 900px;
   height: auto;
   justify-content: center;
   margin: 20px auto;
   box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
   border-radius: 12px;
   background: white;
}

.title {
   width: 100%;
   display: flex;
   flex-direction: column;
   justify-content: center;
   align-items: center;
   padding: 20px;

}

.title h3 {
   font-size: 20px;
   font-weight: bolder;
   letter-spacing: -0.5px;
   line-height: 28px;
   margin: 4px;

}

.title p {
   font-size: 14px;
   color: #64748b70;

   margin: 0px;
}

.form-group {
   display: flex;
   flex-direction: column;
   gap: 15px;
   background-color: rgb(255, 255, 255);
   padding: 20px;
   width: 100%;
   border-radius: 10px;
}

input {
   padding: 13px 10px;
   border: 1px solid #2d2d2d78;
   border-radius: 5px;
   width: 100%;
}

label {
   padding: 4px;
   font-size: 15px;
   font-weight: 500;
}

input[type="submit"] {
   width: fit-content;
   background-color: red;
   color: white;
   border: none;
   padding: 12px 20px;
   font-size: 16px;
   border-radius: 6px;
   cursor: pointer;
   margin-left: auto;

}

/* PAGE ACTIVITE */
.icone.activite {
   background-color: rgba(0, 128, 0, 0.212);
}

.form-group .custom-file-upload {
   position: relative;
   border-radius: 10px;
   display: inline-block;
   width: 100%;
   height: 50px;
   overflow: hidden;
   background-color: transparent;
   border: 1px solid var(--lightgreyColor);
   display: flex;
   align-items: center;
   justify-content: space-between;
   cursor: pointer;
}

.form-group .custom-button-upload {
   cursor: pointer;
   width: 100%;
   height: 50px;
   background-color: white;
   color: black;

   border: none;
   border-radius: 8px;
   text-align: center;
   line-height: 1;
   padding: 15px 0;
}

.form-group .custom-button-upload:hover {
   background-color: #16a34a33;
}

input[type="submit"].activite {
   background-color: #16A34A;
}

;

.icone.experience {
   background-color: #F3E8FF;
}

.form-group.experience {
   gap: 10px;
}

input[type="submit"].experience {
   background-color: #9333EA;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Mobile Styles (320px - 767px) */
@media (max-width: 767px) {
   .container {
      padding: 0 15px;
   }

   section {
      padding: 15px 0;
   }

   .direction {
      margin: 15px 0;
      flex-wrap: wrap;
      gap: 10px;
   }

   .direction span {
      font-size: 14px;
   }

   .titre {
      padding: 0 10px;
   }

   .titre .icone {
      width: 60px;
      height: 60px;
      margin-bottom: 15px;
   }

   .titre h2 {
      font-size: 24px;
      text-align: center;
      margin: 15px 0;
   }

   .titre p {
      font-size: 16px;
      text-align: center;
      padding: 0 10px;
      line-height: 1.5;
   }

   .box {
      width: 100%;
      margin: 15px auto;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
   }

   .title {
      padding: 20px 15px;
   }

   .title h3 {
      font-size: 18px;
      text-align: center;
   }

   .title p {
      font-size: 14px;
      text-align: center;
      line-height: 1.4;
   }

   .form-group {
      padding: 20px 15px;
      gap: 20px;
   }

   input, textarea {
      padding: 15px 12px;
      font-size: 16px; /* Prevents zoom on iOS */
      border-radius: 8px;
      border: 1px solid #ddd;
   }

   textarea {
      min-height: 120px;
      resize: vertical;
   }

   label {
      font-size: 16px;
      font-weight: 600;
      color: #333;
   }

   .custom-file-upload {
      height: 60px !important;
      border-radius: 8px !important;
      border: 2px dashed #ddd !important;
      background: #f8f9fa !important;
   }

   .custom-button-upload {
      height: 60px !important;
      padding: 20px 15px !important;
      font-size: 16px !important;
      font-weight: 500 !important;
      color: #666 !important;
   }

   .custom-button-upload:hover {
      background-color: #e9ecef !important;
   }

   input[type="submit"] {
      width: 100% !important;
      padding: 15px 20px !important;
      font-size: 18px !important;
      font-weight: 600 !important;
      border-radius: 8px !important;
      margin: 20px 0 0 0 !important;
      cursor: pointer;
      transition: all 0.3s ease;
   }

   input[type="submit"]:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
   }

   input[type="submit"]:active {
      transform: translateY(0);
   }

   /* Color-specific mobile styles */
   input[type="submit"].activite:hover {
      background-color: #15803d;
   }

   input[type="submit"].experience:hover {
      background-color: #7c2d12;
   }

   input[type="submit"]:hover {
      background-color: #dc2626;
   }
}

/* Tablet Styles (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
   .container {
      padding: 0 30px;
   }

   .box {
      max-width: 700px;
      margin: 25px auto;
   }

   .titre h2 {
      font-size: 26px;
   }

   .titre p {
      font-size: 17px;
   }

   .form-group {
      padding: 25px 30px;
   }

   input, textarea {
      padding: 12px 15px;
      font-size: 16px;
   }

   input[type="submit"] {
      width: auto !important;
      padding: 12px 30px !important;
      margin-left: auto !important;
   }
}

/* Large Desktop Styles (1024px+) */
@media (min-width: 1024px) {
   .box {
      max-width: 900px;
   }

   .form-group {
      padding: 30px;
   }

   input[type="submit"]:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
   }
}