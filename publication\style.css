* {
   -webkit-box-sizing: border-box;
   -moz-box-sizing: border-box;
   box-sizing: border-box;
}

body {
   margin: 0;
   padding: 0;
   text-decoration: none;
   list-style: none;
   font-family: 'Inter', sans-serif;

}

.container {
   width: 100%;
   margin-left: auto;
   margin-right: auto;

}

:root {
   --mainColor: red;
   --lightgreyColor: #bcbcbc;
}

section {
   width: 100%;
   height: 100vh;
   position: relative;
}

.direction {
   display: flex;
   justify-content: left;
   align-items: center;
   margin: 20px;
}

.direction span {
   font-weight: 200;
   color: #6e6c6c;
   font-size: 16px;
   margin-left: 10px;
}

.titre {
   width: 100%;
   display: flex;
   flex-direction: column;
   align-items: center;

}

.titre .icone {
   width: 50px;
   height: 50px;
   border-radius: 50%;
   background-color: rgba(255, 0, 0, 0.164);
   display: flex;
   justify-content: center;
   align-items: center;
}

.titre h2 {
   font-size: 29px;
   font-weight: bolder;
   margin: 12px;

}

.titre p {
   font-size: 19px;
   color: #6e6c6c66;
   font-weight: 500;
   margin-top: 0;

}

.box {

   DISPLAY: FLEX;

   FLEX-DIRECTION: column;

   WIDTH: 900px;

   HEIGHT: auto;

   JUSTIFY-CONTENT: center;

   margin: auto;

   box-shadow: -1px 9px 5px 7px #80808040;

   border-radius: 8px;
}

.title {
   width: 100%;
   display: flex;
   flex-direction: column;
   justify-content: center;
   align-items: center;
   padding: 20px;

}

.title h3 {
   font-size: 20px;
   font-weight: bolder;
   letter-spacing: -0.5px;
   line-height: 28px;
   margin: 4px;

}

.title p {
   font-size: 14px;
   color: #64748b70;

   margin: 0px;
}

.form-group {
   display: flex;
   flex-direction: column;
   gap: 15px;
   background-color: rgb(255, 255, 255);
   padding: 20px;
   width: 100%;
   border-radius: 10px;
}

input {
   padding: 13px 10px;
   border: 1px solid #2d2d2d78;
   border-radius: 5px;
   width: 100%;
}

label {
   padding: 4px;
   font-size: 15px;
   font-weight: 500;
}

input[type="submit"] {
   width: fit-content;
   background-color: red;
   color: white;
   border: none;
   padding: 12px 20px;
   font-size: 16px;
   border-radius: 6px;
   cursor: pointer;
   margin-left: auto;

}

/* PAGE ACTIVITE */
.icone.activite {
   background-color: rgba(0, 128, 0, 0.212);
}

.form-group .custom-file-upload {
   position: relative;
   border-radius: 10px;
   display: inline-block;
   width: 100%;
   height: 50px;
   overflow: hidden;
   background-color: transparent;
   border: 1px solid var(--lightgreyColor);
   display: flex;
   align-items: center;
   justify-content: space-between;
   cursor: pointer;
}

.form-group .custom-button-upload {
   cursor: pointer;
   width: 100%;
   height: 50px;
   background-color: white;
   color: black;

   border: none;
   border-radius: 8px;
   text-align: center;
   line-height: 1;
   padding: 15px 0;
}

.form-group .custom-button-upload:hover {
   background-color: #16a34a33;
}

input[type="submit"].activite {
   background-color: #16A34A;
}

;

.icone.experience {
   background-color: #F3E8FF;
}

.form-group.experience {
   gap: 10px;
}

input[type="submit"].experience {
   background-color: #9333EA;
}

;