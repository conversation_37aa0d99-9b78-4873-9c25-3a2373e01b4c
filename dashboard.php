<?php
/**
 * Role-based Dashboard
 * Redirects users to appropriate dashboard based on their role
 */

session_start();
require_once "config.php";

// Check if user is logged in
if (!isset($_SESSION["email"]) || !isset($_SESSION["type"])) {
    header("Location: connexion.php");
    exit;
}

$userEmail = $_SESSION["email"];
$userType = $_SESSION["type"];

try {
    if ($userType === 'association') {
        // Association dashboard
        $stmt = $pdo->prepare("SELECT * FROM association WHERE EMAIL = :email");
        $stmt->execute([':email' => $userEmail]);
        $user = $stmt->fetch();
        
        if (!$user) {
            session_destroy();
            header("Location: connexion.php");
            exit;
        }
        
        // Check if association is verified
        if (!$user['VERIFIE']) {
            header("Location: association_pending.php");
            exit;
        }
        
        // Check if user is admin type association
        if ($user['EMAIL'] === '<EMAIL>') {
            header("Location: admin/index.php");
            exit;
        }
        
        // Regular association - go to association profile
        header("Location: profile-association.php");
        exit;
        
    } else {
        // Regular user dashboard
        $stmt = $pdo->prepare("SELECT * FROM utilisateur WHERE EMAIL = :email");
        $stmt->execute([':email' => $userEmail]);
        $user = $stmt->fetch();
        
        if (!$user) {
            session_destroy();
            header("Location: connexion.php");
            exit;
        }
        
        // Check if user is admin
        if ($user['TYPE_UTILISATEUR'] == 1) {
            header("Location: admin/index.php");
            exit;
        }
        
        // Regular user - go to news feed
        header("Location: actualite.php");
        exit;
    }
    
} catch (PDOException $e) {
    error_log("Dashboard error: " . $e->getMessage());
    header("Location: actualite.php?error=dashboard_error");
    exit;
}

// Fallback
header("Location: actualite.php");
exit;
?>
