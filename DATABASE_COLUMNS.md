# YADFYAD Database Column Reference

This document lists the actual column names in the database to prevent confusion.

## 📊 Table: `association`
- `ID_ASSOCIATION` (Primary Key)
- `NOM_ASSOCIATION`
- `EMAIL`
- `MOT_DE_PASSE`
- `INFO`
- `ADRESSE`
- `NUMERO_TELEPHONE`
- `DOMAINE`
- `VERIFIE`
- `STATUT`
- `DATE_CREATION` ⚠️ (NOT `date_creation`)
- `DATE_MISE_A_JOUR` ⚠️ (NOT `date_modification`)

## 👤 Table: `utilisateur`
- `ID_UTILISATEUR` (Primary Key)
- `NOM`
- `PRENOM`
- `EMAIL`
- `MOT_DE_PASSE`
- `DESCRIPTION`
- `TYPE_UTILISATEUR`
- `ID_ASSOCIATION` (Foreign Key)
- `DATE_CREATION` ⚠️ (NOT `date_creation`)
- `DATE_MISE_A_JOUR` ⚠️ (NOT `date_modification`)

## 📝 Table: `publication`
- `ID_PUB` (Primary Key)
- `ID_UTILISATEUR` (Foreign Key)
- `TYPE_PUB`
- `TITRE`
- `DISCRIPTION`
- `LOCALISATION_SPICIFIQUE`
- `DATE_EVENEMENT_ACTIVITE`
- `LIEU_EVENEMENT_LACTIVITE`
- `RESUME`
- `DATE_CREATION` ⚠️ (NOT `date_creation`)
- `DATE_MISE_A_JOUR` ⚠️ (NOT `date_modification`)

## 📷 Table: `medias_url`
- `ID_MEDIA` (Primary Key)
- `NOM_MEDIA`
- `ID_PUB` (Foreign Key)
- `DATE_CREATION` ⚠️ (NOT `date_creation`)

## ❤️ Table: `liker`
- `ID_LIKE` (Primary Key)
- `ID_UTILISATEUR` (Foreign Key)
- `ID_PUB` (Foreign Key)
- `DATE_CREATION` ⚠️ (NOT `date_creation`)

## 👥 Table: `suivre`
- `ID_SUIVRE` (Primary Key)
- `ID_SUIVEUR` (Foreign Key)
- `ID_SUIVI` (Foreign Key)
- `DATE_CREATION` ⚠️ (NOT `date_creation`)

## 💬 Table: `commentaire`
- `ID_COMMENTAIRE` (Primary Key)
- `CONTENU`
- `ID_UTILISATEUR` (Foreign Key)
- `ID_PUB` (Foreign Key)
- `DATE_CREATION` ⚠️ (NOT `date_creation`)

## 💬 Table: `chater`
- `ID_MESSAGE` (Primary Key)
- `sender_id` (Foreign Key)
- `receiver_id` (Foreign Key)
- `message`
- `created_at` ⚠️ (Different naming convention)

## ⚠️ Important Notes:

1. **Date Columns**: Most tables use `DATE_CREATION` and `DATE_MISE_A_JOUR` (uppercase)
2. **Chat Table**: Uses `created_at` (lowercase with underscore)
3. **User Table**: Uses `ID_UTILISATEUR` (not `user_id`)
4. **Publication Table**: Has additional columns not in original schema

## 🔧 Common Fixes Applied:

### Files Updated for Correct Column Names:
- ✅ `actualite.php` - Fixed all date column references
- ✅ `admin/publications.php` - Fixed DATE_CREATION reference
- ✅ `admin/utilisateurs.php` - Fixed DATE_CREATION and DATE_MISE_A_JOUR
- ✅ `admin/index.php` - Fixed analytics query date columns
- ✅ `profile.php` - Fixed DATE_CREATION reference
- ✅ `quick_login_test.php` - Fixed test query

### Query Pattern Examples:

**Correct:**
```sql
SELECT publication.DATE_CREATION, publication.DATE_MISE_A_JOUR 
FROM publication
```

**Incorrect:**
```sql
SELECT publication.date_creation, publication.date_modification 
FROM publication
```

## 🎯 Status: All column name issues resolved! ✅
