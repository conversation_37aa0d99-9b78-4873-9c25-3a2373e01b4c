* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-decoration: none;
    list-style: none;
   
 }
 body{
    margin: 0;
    padding: 0;
    font-family: "Inter", sans-serif;
    font-optical-sizing: auto;
    font-style: normal;
}
a{
    color: black;
}
.container{
   width: 1240px;
    padding-left:60px;
    padding-right:60px;
    margin-left: auto;
    margin-right: auto;  
}
:root{
    --mainColor:#0048A6;
    --buttonColor:#267CF4;
    --lightGrey:#71717aaf;
    --backgoundcolor:#f1f5f9;
    --textColor:#000000;
    --colorparagraph:#6b7280;
    --greenColor:#059669;
}
.head-profil {
    display: flex;
    align-items: center;
    gap: 25px;
}
.photo-profil {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: var(--lightGrey);
}
.script-profil{
  width: 700px;
  padding: 20px 0;
  flex: 1;
}
.script-profil h2{
    font-size: 24px;
    font-weight: 600;
    color: var(--textColor);
    margin-bottom: 10px;
}
.script-profil p{
    font-size: 16px;
    color: var(--colorparagraph);
    line-height: 1.5;
}
.lieu{
      display: flex;
    align-items: center;
    color: grey;
}
.bouttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 20px;
    align-self: end;
}
button#follow {
    background-color: var(--greenColor);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 16px;
    font-weight: 500;
}
.nombre{
  display: flex;
  align-items: center;
  /* gap: 2px; */
}
.nombre .abonnes .membres {
  width: 100px;
   border: 1px solid var(--lightGrey);
}
.nombre span {
    font-size: 16px;
    color: var(--textColor);
    font-weight: 600;
    padding: 5px 10px;
    border-radius: 5px;
}
.abonnes {
    border: 2px solid lightgray;
    padding: 8px 74px;
    border-radius: 9px 0 0 9px;
}
.membres {
    border: 2px solid lightgray;
    padding: 8px 74px;
   border-radius: 0 9px 9px 0;
}
.links {
    display: flex;
    justify-content: center;
    align-items: center;
    /* gap: 258px; */
    /* border: 2px solid lightgray; */
    border-radius: 8px;
    background: #f1f5f9;
    box-shadow: 2px 4PX 5PX 0PX rgb(128 128 128 / 24%);
}
.links li {
        padding: 11px 144px;
}
/* publications */


.posts {
    width: 768px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    margin: 0 auto;
}

.post {
    width: 100%;
    background-color: white;
    border: 1px solid var(--lightGrey);
    border-radius: 10px;
    margin: 12px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.post-image {
    background-color: var(--lightGrey);
    margin-bottom: 10px;
}

.post-image img {
    width: 100%;
}

.post .post-container {
    padding: 16px;
}

.post .post-header {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
}

.post .post-header .post-icone {
    width: 40px;
    height: 40px;
    background-color: var(--lightGrey);
    border-radius: 50%;
}

.post .post-header .post-header-contenu {
    display: flex;
    flex-direction: column;
}

.post .post-header .post-type {
    font-size: 14px;
    font-weight: 500;
}

.post .post-header .post-date {
    font-size: 12px;
    font-weight: 400;
    color: var(--lightGrey);
}

.post .post-contenu {
    width: 100%;
    margin-top: 10px;
}

.post .post-contenu .post-titre {
    font-size: 18px;
    font-weight: 600;
    color: var(--textColor);
    margin: 10px 0;
}

.post .post-contenu .post-description {
    font-size: 14px;
    font-weight: 500;
    color: var(--textColor);
    margin: 10px 0;
}

.post .post-contenu .post-association {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
}

.post .post-contenu .post-association .post-association-avatar {
    width: 30px;
    height: 30px;
    background-color: var(--lightGrey);
    border-radius: 50%;
}

.post .post-contenu .post-association .post-association-nom {
    font-size: 14px;
    font-weight: 500;
    color: var(--textColor);
}

.post .post-interactions {
    width: 100%;
    display: flex;
    align-items: center;
    margin-top: 15px;
    border-top: 1px solid var(--lightGrey);
    padding-top: 10px;
    gap: 10px;
}

.post .post-interactions .post-interaction-element {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    font-weight: 500;
    color: var(--textColor);
}