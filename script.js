//==Enhanced Responsive nav-bar====
let navMenu = document.getElementById("side-bar");
let navToggle = document.getElementById("buttonClick");
const body = document.body;

// Enhanced mobile menu toggle
if (navToggle && navMenu) {
    navToggle.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        navMenu.classList.toggle('show-menu');
        navToggle.classList.toggle('active');

        // Prevent body scroll when menu is open on mobile
        if (window.innerWidth <= 768) {
            if (navMenu.classList.contains('show-menu')) {
                body.style.overflow = 'hidden';
            } else {
                body.style.overflow = '';
            }
        }
    });
}

// Close menu when clicking outside (mobile only)
document.addEventListener('click', (e) => {
    if (window.innerWidth <= 768 && navMenu && navMenu.classList.contains('show-menu')) {
        if (!navMenu.contains(e.target) && !navToggle.contains(e.target)) {
            navMenu.classList.remove('show-menu');
            navToggle?.classList.remove('active');
            body.style.overflow = '';
        }
    }
});

// Close menu when clicking on navigation links (mobile only)
navMenu?.querySelectorAll('a').forEach(link => {
    link.addEventListener('click', () => {
        if (window.innerWidth <= 768) {
            navMenu.classList.remove('show-menu');
            navToggle?.classList.remove('active');
            body.style.overflow = '';
        }
    });
});

// Handle window resize
window.addEventListener('resize', () => {
    if (window.innerWidth > 768) {
        navMenu?.classList.remove('show-menu');
        navToggle?.classList.remove('active');
        body.style.overflow = '';
    }
});

// Nouvelle publication
const newPublicationButton = document.querySelector(".new-publication-trigger");
const newPublicationDropdown = document.querySelector(".new-publication-types");
const newPublicationCloseOverlay = document.querySelector(".new-publication-close-overlay");

newPublicationButton?.addEventListener("click", () => {
    newPublicationDropdown.classList.contains("active") ? newPublicationDropdown.classList.remove("active") : newPublicationDropdown.classList.add("active");
    newPublicationDropdown.classList.contains("active") ? newPublicationCloseOverlay.classList.add("active") : newPublicationCloseOverlay.classList.remove("active");
})

newPublicationCloseOverlay?.addEventListener("click", () => {
    newPublicationDropdown.classList.remove("active");
    newPublicationCloseOverlay.classList.remove("active");
})

// About Association tabs
const associationTabsTriggers = document.querySelectorAll(".association-profile-tabs-triggers .tab-trigger");
const associationTabs = document.querySelectorAll(".association-profile-tabs .tab")

associationTabsTriggers.forEach(associationTabTrigger => {
    const tabName = associationTabTrigger.dataset.tab;
    associationTabTrigger.addEventListener("click", () => {
        Array.from(associationTabTrigger.parentElement.children).forEach(child => child.classList.remove("active"));
        associationTabTrigger.classList.add("active");

        const associationTab = Array.from(associationTabs).find(associationTab => associationTab.dataset.tab == tabName);
        Array.from(associationTab.parentElement.children).forEach(child => child.classList.remove("active"));
        associationTab.classList.add("active");
    })
})
//==Chat auto-scroll and responsive features====
window.onload = () => {
    const msgBox = document.querySelector('.chat-messages');
    if (msgBox) {
        msgBox.scrollTop = msgBox.scrollHeight;
    }

    // Initialize responsive features
    initResponsiveFeatures();
};

// Responsive features initialization
function initResponsiveFeatures() {
    // Touch-friendly interactions for mobile
    if ('ontouchstart' in window) {
        document.body.classList.add('touch-device');

        // Add touch feedback to interactive elements
        const interactiveElements = document.querySelectorAll('.post, .card, button, .btn, a');
        interactiveElements.forEach(element => {
            element.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
            });

            element.addEventListener('touchend', function() {
                this.style.transform = '';
            });
        });
    }

    // Optimize images for mobile
    optimizeImagesForMobile();

    // Handle orientation change
    window.addEventListener('orientationchange', handleOrientationChange);

    // Improve form experience on mobile
    improveMobileFormExperience();
}

// Optimize images for mobile devices
function optimizeImagesForMobile() {
    if (window.innerWidth <= 768) {
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            // Add loading="lazy" for better performance
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy');
            }

            // Ensure images are responsive
            if (!img.style.maxWidth) {
                img.style.maxWidth = '100%';
                img.style.height = 'auto';
            }
        });
    }
}

// Handle orientation change
function handleOrientationChange() {
    setTimeout(() => {
        // Close mobile menu on orientation change
        if (navMenu?.classList.contains('show-menu')) {
            navMenu.classList.remove('show-menu');
            navToggle?.classList.remove('active');
            body.style.overflow = '';
        }

        // Recalculate chat height if on chat page
        const chatMessages = document.querySelector('.chat-messages');
        if (chatMessages && window.innerWidth <= 768) {
            chatMessages.style.height = `calc(100vh - 350px)`;
        }
    }, 100);
}

// Improve mobile form experience
function improveMobileFormExperience() {
    const inputs = document.querySelectorAll('input, textarea, select');

    inputs.forEach(input => {
        // Prevent zoom on focus for iOS
        if (input.type !== 'file') {
            const currentFontSize = window.getComputedStyle(input).fontSize;
            if (parseFloat(currentFontSize) < 16) {
                input.style.fontSize = '16px';
            }
        }

        // Add better focus styles for mobile
        input.addEventListener('focus', function() {
            if (window.innerWidth <= 768) {
                this.style.borderColor = '#007bff';
                this.style.boxShadow = '0 0 0 2px rgba(0,123,255,0.25)';
            }
        });

        input.addEventListener('blur', function() {
            this.style.borderColor = '';
            this.style.boxShadow = '';
        });
    });
}

// Smooth scroll for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add swipe gesture support for mobile navigation
if ('ontouchstart' in window && navMenu) {
    let startX = 0;
    let startY = 0;

    document.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
    });

    document.addEventListener('touchmove', (e) => {
        if (!startX || !startY) return;

        const diffX = startX - e.touches[0].clientX;
        const diffY = startY - e.touches[0].clientY;

        // Swipe right to open menu (from left edge)
        if (startX < 50 && diffX < -100 && Math.abs(diffY) < 100) {
            if (!navMenu.classList.contains('show-menu')) {
                navMenu.classList.add('show-menu');
                navToggle?.classList.add('active');
                body.style.overflow = 'hidden';
            }
        }

        // Swipe left to close menu
        if (navMenu.classList.contains('show-menu') && diffX > 100 && Math.abs(diffY) < 100) {
            navMenu.classList.remove('show-menu');
            navToggle?.classList.remove('active');
            body.style.overflow = '';
        }

        startX = 0;
        startY = 0;
    });
}
