<?php
/**
 * Database Setup Script for YADFYAD
 * This script creates the database and all necessary tables
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'yadfyad';

try {
    // First, connect without specifying database to create it
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to MySQL server successfully.\n";
    
    // Read and execute the SQL file
    $sql = file_get_contents('database_setup.sql');
    
    if ($sql === false) {
        throw new Exception("Could not read database_setup.sql file");
    }
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                echo "✓ Executed: " . substr($statement, 0, 50) . "...\n";
            } catch (PDOException $e) {
                echo "✗ Error executing statement: " . $e->getMessage() . "\n";
                echo "Statement: " . substr($statement, 0, 100) . "...\n";
            }
        }
    }
    
    echo "\n=== Database Setup Complete ===\n";
    echo "Database 'yadfyad' has been created with all necessary tables.\n";
    echo "Sample data has been inserted for testing.\n\n";
    
    echo "Test credentials:\n";
    echo "Association: <EMAIL> / password\n";
    echo "User: <EMAIL> / password\n";
    echo "Admin: <EMAIL> / password\n";
    
} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
