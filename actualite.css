* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-decoration: none;
    list-style: none;

}

body {
    margin: 0;
    padding: 0;
    font-family: "Inter", sans-serif;
    font-optical-sizing: auto;
    font-style: normal;
}

a {
    color: black;
}

.container {
    width: 100%;
    max-width: 1200px;
    padding-left: 4%;
    padding-right: 4%;
    margin-left: auto;
    margin-right: auto;
}

:root {
    --mainColor: #0048A6;
    --lightMainColor: #0048A680;
    --buttonColor: #267CF4;
    --lightGrey: #71717aaf;
    --backgoundcolor: #f1f5f9;
    --textColor: #000000;
    --experienceColor: #9333EA;
    --bgExperience: #f3e8ff;
    --problemeColor: #dc2626;
    --bgProbleme: #fee2e2;
    --activiteColor: #16a34a;
    --bgActivite: #dcfce7;
    --greenColor: #059669;
    --bgGreen: #0596686d;
}

section {
    width: 100%;
    margin: 0;
    padding: 0;
    position: relative;

}

.header-actualite {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 20px;
}

.header-actualite .links-tous {
    width: 60%;
    display: flex;
    justify-content: space-between;
    padding: 10px 0;

}

.header-actualite .links-tous ul {
    display: flex;
    align-items: center;
    background-color: var(--backgoundcolor);
    gap: 20px;
    padding: 10px 5px;
    margin: auto 0;
    border-radius: 8px;
}

.header-actualite .links-tous ul li a {
    font-size: 14px;
    font-weight: 500;
    color: var(--lightGrey);
    padding: 6px 10px;
    transition: all .3s ease-in-out;
    display: block;
}

.header-actualite .links-tous ul li a:focus {
    background-color: white;
    border-radius: 8px;
    color: var(--textColor);
    font-size: 16px;
}

.header-actualite .recherche {
    width: 40%;
    display: flex;
    align-items: center;
}

.header-actualite .recherche input {
    width: 340px;
    border: 1px solid var(--lightGrey);
    padding: 8px 10px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
    box-shadow: -1px 1px 5px 4px rgba(0, 0, 0, 0.1);
}

.posts {
    width: 768px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
}

.post {
    width: 100%;
    background-color: white;
    border: 1px solid var(--lightGrey);
    border-radius: 10px;
    margin: 0 0 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.post-image {
    background-color: var(--lightGrey);
    border-radius: 12px;
    overflow: hidden;
}

.post-image:not(:empty) {
    margin-top: 20px;
}

.post-image img {
    width: 100%;
}

.post-info {
    display: flex;
    gap: 12px;
    color: #999;
    font-size: 16px;
    padding-top: 20px;
}

.post .post-info .post-info-element {
    display: flex;
    gap: 7px;
    align-items: center;
}

.post .post-container {
    padding: 16px;
}

.post .post-header {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
}

.post .post-header .post-icone {
    width: 50px;
    height: 50px;
    background-color: var(--lightGrey);
    border-radius: 50%;
}

.post .post-header .post-header-contenu {
    display: flex;
    flex-direction: column;
    flex: 1;
}


.post .post-header .post-type {
    padding: 7px 10px;
    border-radius: 999px;
    color: var(--mainColor);
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12PX;
    font-weight: 600;
}

.post .post-header .post-type.experience {
    color: var(--experienceColor);
    background-color: var(--bgExperience);
}

.post .post-header .post-type.activite {
    color: var(--activiteColor);
    background-color: var(--bgActivite);
}

.post .post-header .post-type.probleme {
    color: var(--problemeColor);
    background-color: var(--bgProbleme);
}

.post .post-header .post-type span {
    text-transform: capitalize;
    padding-top: 3px;
}

.post .post-header .post-association {
    font-size: 18px;
    font-weight: bold;
    text-transform: capitalize;
    padding-bottom: 3px;
}

.post .post-header .post-date {
    font-size: 16px;
    font-weight: 400;
    color: var(--lightGrey);
}

.post .post-contenu {
    width: 100%;
    margin-top: 10px;
}

.post .post-contenu .post-titre {
    font-size: 20px;
    font-weight: 600;
    color: var(--textColor);
    margin: 15px 0;
}

.post .post-contenu .post-description {
    font-size: 17px;
    font-weight: 500;
    color: #999999;
    padding-top: 10px;
}

.post .post-interactions {
    width: 100%;
    display: flex;
    align-items: center;
    margin-top: 20px;
    border-top: 1px solid var(--lightGrey);
    padding: 20px 20px 0 20px;
    gap: 30px;
}

.post .post-interactions .post-interaction-element {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 16px;
    font-weight: 500;
    color: var(--textColor);
}

.post .post-interactions .post-interaction-element .icone {
    cursor: pointer;
}

.post .post-interactions .post-interaction-element .liking svg {
    fill: rgb(255, 0, 0);
    stroke: rgb(255, 0, 0)
}

/* ===== RESPONSIVE DESIGN ===== */

/* Mobile Styles (320px - 767px) */
@media (max-width: 767px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }

    .header-actualite {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .header-actualite .links-tous {
        width: 100%;
        order: 2;
    }

    .header-actualite .links-tous ul {
        justify-content: center;
        flex-wrap: wrap;
        gap: 10px;
        padding: 8px;
    }

    .header-actualite .links-tous ul li a {
        font-size: 12px;
        padding: 8px 12px;
        white-space: nowrap;
    }

    .header-actualite .recherche {
        width: 100%;
        order: 1;
        justify-content: center;
    }

    .header-actualite .recherche input {
        width: 100%;
        max-width: 400px;
        padding: 12px 15px;
        font-size: 16px;
        border-radius: 8px;
    }

    .posts {
        width: 100%;
        padding: 0;
    }

    .post {
        margin: 0 0 20px;
        border-radius: 8px;
    }

    .post .post-container {
        padding: 15px;
    }

    .post .post-header {
        flex-wrap: wrap;
        gap: 12px;
    }

    .post .post-header .post-icone {
        width: 45px;
        height: 45px;
    }

    .post .post-header .post-header-contenu {
        flex: 1;
        min-width: 0;
    }

    .post .post-header .post-association {
        font-size: 16px;
        line-height: 1.3;
    }

    .post .post-header .post-date {
        font-size: 14px;
    }

    .post .post-header .post-type {
        order: 3;
        width: 100%;
        justify-content: center;
        margin-top: 8px;
        padding: 8px 12px;
        font-size: 12px;
    }

    .post .post-contenu .post-titre {
        font-size: 18px;
        line-height: 1.4;
        margin: 12px 0;
    }

    .post .post-contenu .post-description {
        font-size: 16px;
        line-height: 1.5;
        padding-top: 8px;
    }

    .post-info {
        flex-wrap: wrap;
        gap: 15px;
        font-size: 14px;
        padding-top: 15px;
    }

    .post .post-info .post-info-element {
        gap: 5px;
    }

    .post .post-interactions {
        padding: 15px 15px 0 15px;
        gap: 20px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .post .post-interactions .post-interaction-element {
        font-size: 14px;
        gap: 8px;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 20px;
        transition: background-color 0.3s ease;
    }

    .post .post-interactions .post-interaction-element:active {
        background: #e9ecef;
        transform: scale(0.95);
    }

    .post-image {
        margin-top: 15px;
        border-radius: 8px;
    }
}

/* Tablet Styles (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .container {
        padding-left: 20px;
        padding-right: 20px;
    }

    .header-actualite .recherche input {
        width: 280px;
    }

    .posts {
        width: 100%;
        max-width: 600px;
    }

    .post .post-header .post-association {
        font-size: 17px;
    }

    .post .post-contenu .post-titre {
        font-size: 19px;
    }

    .post .post-interactions {
        gap: 25px;
    }
}

/* Large screens adjustments */
@media (min-width: 1024px) {
    .posts {
        max-width: 768px;
    }
}