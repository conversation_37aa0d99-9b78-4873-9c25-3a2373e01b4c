* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-decoration: none;
    list-style: none;

}

body {
    margin: 0;
    padding: 0;
    font-family: "Inter", sans-serif;
    font-optical-sizing: auto;
    font-style: normal;
}

a {
    color: black;
}

.container {
    width: 100%;
    padding-left: 4%;
    padding-right: 4%;
    margin-left: auto;
    margin-right: auto;


}

:root {
    --mainColor: #0048A6;
    --lightMainColor: #0048A680;
    --buttonColor: #267CF4;
    --lightGrey: #71717aaf;
    --backgoundcolor: #f1f5f9;
    --textColor: #000000;
    --experienceColor: #9333EA;
    --bgExperience: #f3e8ff;
    --problemeColor: #dc2626;
    --bgProbleme: #fee2e2;
    --activiteColor: #16a34a;
    --bgActivite: #dcfce7;
    --greenColor: #059669;
    --bgGreen: #0596686d;
}

section {
    width: 100%;
    margin: 0;
    padding: 0;
    position: relative;

}

.header-actualite {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 20px;
}

.header-actualite .links-tous {
    width: 60%;
    display: flex;
    justify-content: space-between;
    padding: 10px 0;

}

.header-actualite .links-tous ul {
    display: flex;
    align-items: center;
    background-color: var(--backgoundcolor);
    gap: 20px;
    padding: 10px 5px;
    margin: auto 0;
    border-radius: 8px;
}

.header-actualite .links-tous ul li a {
    font-size: 14px;
    font-weight: 500;
    color: var(--lightGrey);
    padding: 6px 10px;
    transition: all .3s ease-in-out;
    display: block;
}

.header-actualite .links-tous ul li a:focus {
    background-color: white;
    border-radius: 8px;
    color: var(--textColor);
    font-size: 16px;
}

.header-actualite .recherche {
    width: 40%;
    display: flex;
    align-items: center;
}

.header-actualite .recherche input {
    width: 340px;
    border: 1px solid var(--lightGrey);
    padding: 8px 10px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
    box-shadow: -1px 1px 5px 4px rgba(0, 0, 0, 0.1);
}

.posts {
    width: 768px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
}

.post {
    width: 100%;
    background-color: white;
    border: 1px solid var(--lightGrey);
    border-radius: 10px;
    margin: 0 0 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.post-image {
    background-color: var(--lightGrey);
    border-radius: 12px;
    overflow: hidden;
}

.post-image:not(:empty) {
    margin-top: 20px;
}

.post-image img {
    width: 100%;
}

.post-info {
    display: flex;
    gap: 12px;
    color: #999;
    font-size: 16px;
    padding-top: 20px;
}

.post .post-info .post-info-element {
    display: flex;
    gap: 7px;
    align-items: center;
}

.post .post-container {
    padding: 16px;
}

.post .post-header {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
}

.post .post-header .post-icone {
    width: 50px;
    height: 50px;
    background-color: var(--lightGrey);
    border-radius: 50%;
}

.post .post-header .post-header-contenu {
    display: flex;
    flex-direction: column;
    flex: 1;
}


.post .post-header .post-type {
    padding: 7px 10px;
    border-radius: 999px;
    color: var(--mainColor);
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12PX;
    font-weight: 600;
}

.post .post-header .post-type.experience {
    color: var(--experienceColor);
    background-color: var(--bgExperience);
}

.post .post-header .post-type.activite {
    color: var(--activiteColor);
    background-color: var(--bgActivite);
}

.post .post-header .post-type.probleme {
    color: var(--problemeColor);
    background-color: var(--bgProbleme);
}

.post .post-header .post-type span {
    text-transform: capitalize;
    padding-top: 3px;
}

.post .post-header .post-association {
    font-size: 18px;
    font-weight: bold;
    text-transform: capitalize;
    padding-bottom: 3px;
}

.post .post-header .post-date {
    font-size: 16px;
    font-weight: 400;
    color: var(--lightGrey);
}

.post .post-contenu {
    width: 100%;
    margin-top: 10px;
}

.post .post-contenu .post-titre {
    font-size: 20px;
    font-weight: 600;
    color: var(--textColor);
    margin: 15px 0;
}

.post .post-contenu .post-description {
    font-size: 17px;
    font-weight: 500;
    color: #999999;
    padding-top: 10px;
}

.post .post-interactions {
    width: 100%;
    display: flex;
    align-items: center;
    margin-top: 20px;
    border-top: 1px solid var(--lightGrey);
    padding: 20px 20px 0 20px;
    gap: 30px;
}

.post .post-interactions .post-interaction-element {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 16px;
    font-weight: 500;
    color: var(--textColor);
}

.post .post-interactions .post-interaction-element .icone {
    cursor: pointer;
}

.post .post-interactions .post-interaction-element .liking svg {
    fill: rgb(255, 0, 0);
    stroke: rgb(255, 0, 0)
}