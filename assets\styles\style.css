* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-decoration: none;
    list-style: none;
 }
 body{
    margin: 0;
    padding: 0;
    font-family: 'Inter', sans-serif;
    font-optical-sizing: auto;
    font-style: normal;
}
a{
    color: black;
}
header .container{
    width: 100%;
    padding-left: 3%;
    padding-right: 3%;
    margin-left: auto;
    margin-right: auto;
    
}
:root{
    --mainColor:#0048A6;
    --buttonColor:#267CF4;
}
header{
   width: 100%;
   margin: 0;
   padding: 0;
   position: relative;
   box-shadow: 0px 0.5px 6px 0px rgba(128, 128, 128, 0.748);


}
header nav{
    display: flex;
    width: 100%;
    align-items: center;
   height: 55px;
}
header nav .logo{
    width: 20%;
}
header nav .logo img{
    height: 50PX;
}
header nav .link{
    width: 40%;
}
header nav .link ul{
     display: flex;
     justify-content: center;
     gap: 20px;
}
header nav .link ul li a {
    display: block;
    padding: 15px 0;
    color: black;
    font-size: 16px;
    font-weight: 500;
}
header nav .icons{
    width: 40%;

}
header nav .icons ul{
    display: flex;
    justify-content: end;
    gap:20px;
}
nav .icone {
    display: block;
    position: absolute;
    right: 20px; 
    top: 20px; 
} 
nav .icone span{
    display: none;
    width: 20px;
    height: 1px;
    background-color: rgb(0, 0, 0);
    margin-bottom: 5px;
}
.menu-mobile{
    display: none;
}
@media(max-width:768px){
    nav .icone span{
        display: block;
    }
    header nav{
        display: flex;
        width: 100%;
        align-items: center;
    }
    header nav .icons ul{
        display: none;
    }
    header nav .link ul{
        display: none;
    }
    .menu-mobile{
        display: flex;
        width: 100vw;
    }
    .link-mobile{
        width: 100%;
    }
    .link-mobile ul{
        display: flex;
        justify-content: end;
        gap: 20px;
        margin-right: 40px;
    }
    .side-bar{
        width: 100vw;
        position: absolute;
        top: 40px;
        left: -100%;

    }
    .side-bar ul li a {
        display: flex;
        padding: 10px 0;
        align-items: center;
        gap: 10px;
    }
    .side-bar ul li a:hover {
        border-bottom: var(--mainColor) solid 1px;
        color: var(--mainColor);
        margin: 1rem;
        font-size: 22px;
    }
    .show-menu{
        left:0;
    }

    }
    
/* // ====pour nav bar de index.php-page===/// */
header nav .button{
    width: 40%;

}
header nav .button ul{
    display: flex;
    justify-content: end;
    gap: 3%;
}
header nav .button ul li a {
      display: block;
    background-color: var(--buttonColor);
    padding: 7PX 18PX;
    border-radius: 4px;
    font-size: 15px;
    color: white;
    font-weight: 500;
}
header nav .button ul li a.white-button{
    background-color: white;
    color: var(--buttonColor);
    border: var(--buttonColor) 1px solid;
}
