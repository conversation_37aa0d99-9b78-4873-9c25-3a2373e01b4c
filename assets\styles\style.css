* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-decoration: none;
    list-style: none;
 }
 body{
    margin: 0;
    padding: 0;
    font-family: 'Inter', sans-serif;
    font-optical-sizing: auto;
    font-style: normal;
}
a{
    color: black;
}
header .container{
    width: 100%;
    padding-left: 3%;
    padding-right: 3%;
    margin-left: auto;
    margin-right: auto;
    
}
:root{
    --mainColor:#0048A6;
    --buttonColor:#267CF4;
}
header{
   width: 100%;
   margin: 0;
   padding: 0;
   position: relative;
   box-shadow: 0px 0.5px 6px 0px rgba(128, 128, 128, 0.748);


}
header nav{
    display: flex;
    width: 100%;
    align-items: center;
   height: 55px;
}
header nav .logo{
    width: 20%;
}
header nav .logo img{
    height: 50PX;
}
header nav .link{
    width: 40%;
}
header nav .link ul{
     display: flex;
     justify-content: center;
     gap: 20px;
}
header nav .link ul li a {
    display: block;
    padding: 15px 0;
    color: black;
    font-size: 16px;
    font-weight: 500;
}
header nav .icons{
    width: 40%;

}
header nav .icons ul{
    display: flex;
    justify-content: end;
    gap:20px;
}
nav .icone {
    display: block;
    position: absolute;
    right: 20px; 
    top: 20px; 
} 
nav .icone span{
    display: none;
    width: 20px;
    height: 1px;
    background-color: rgb(0, 0, 0);
    margin-bottom: 5px;
}
.menu-mobile{
    display: none;
}
/* Mobile Navigation - Enhanced */
.menu-mobile {
    display: none;
    cursor: pointer;
    padding: 10px;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.menu-mobile:hover {
    background-color: #f0f0f0;
}

.menu-mobile .icone span {
    display: block;
    width: 25px;
    height: 3px;
    background-color: var(--mainColor);
    margin: 5px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Hamburger animation */
.menu-mobile.active .icone span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.menu-mobile.active .icone span:nth-child(2) {
    opacity: 0;
}

.menu-mobile.active .icone span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

.side-bar {
    position: fixed;
    top: 55px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 55px);
    background: white;
    z-index: 1000;
    transition: left 0.3s ease-in-out;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow-y: auto;
}

.side-bar.show-menu {
    left: 0;
}

.side-bar ul {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.side-bar ul li a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    border-radius: 8px;
    background: #f8f9fa;
    color: #333;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.side-bar ul li a:hover {
    background: var(--mainColor);
    color: white;
    transform: translateX(5px);
    border-color: var(--mainColor);
}

.side-bar ul li a svg {
    width: 20px;
    height: 20px;
}

@media(max-width:768px){
    nav .icone span{
        display: block;
    }
    header nav{
        display: flex;
        width: 100%;
        align-items: center;
        padding: 0 15px;
    }
    header nav .icons ul{
        display: none;
    }
    header nav .link ul{
        display: none;
    }
    header nav .button {
        display: none;
    }
    .menu-mobile{
        display: flex;
        margin-left: auto;
    }
}
    
/* // ====pour nav bar de index.php-page===/// */
header nav .button{
    width: 40%;

}
header nav .button ul{
    display: flex;
    justify-content: end;
    gap: 3%;
}
header nav .button ul li a {
      display: block;
    background-color: var(--buttonColor);
    padding: 7PX 18PX;
    border-radius: 4px;
    font-size: 15px;
    color: white;
    font-weight: 500;
}
header nav .button ul li a.white-button{
    background-color: white;
    color: var(--buttonColor);
    border: var(--buttonColor) 1px solid;
}
