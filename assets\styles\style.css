* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-decoration: none;
    list-style: none;
 }
 body{
    margin: 0;
    padding: 0;
    font-family: 'Inter', sans-serif;
    font-optical-sizing: auto;
    font-style: normal;
}
a{
    color: black;
}
header .container{
    width: 100%;
    padding-left: 3%;
    padding-right: 3%;
    margin-left: auto;
    margin-right: auto;
    
}
:root{
    --mainColor:#0048A6;
    --buttonColor:#267CF4;
}
header{
   width: 100%;
   margin: 0;
   padding: 0;
   position: relative;
   box-shadow: 0px 0.5px 6px 0px rgba(128, 128, 128, 0.748);


}
header nav{
    display: flex;
    width: 100%;
    align-items: center;
   height: 55px;
}
header nav .logo{
    width: 20%;
}
header nav .logo img{
    height: 50PX;
}
header nav .link{
    width: 40%;
}
header nav .link ul{
     display: flex;
     justify-content: center;
     gap: 20px;
}
header nav .link ul li a {
    display: block;
    padding: 15px 0;
    color: black;
    font-size: 16px;
    font-weight: 500;
}
header nav .icons{
    width: 40%;

}
header nav .icons ul{
    display: flex;
    justify-content: end;
    gap:20px;
}
nav .icone {
    display: block;
    position: absolute;
    right: 20px; 
    top: 20px; 
} 
nav .icone span{
    display: none;
    width: 20px;
    height: 1px;
    background-color: rgb(0, 0, 0);
    margin-bottom: 5px;
}
.menu-mobile{
    display: none;
}
/* Mobile Navigation - Enhanced */
.menu-mobile {
    display: none;
    cursor: pointer;
    padding: 10px;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.menu-mobile:hover {
    background-color: #f0f0f0;
}

.menu-mobile .icone span {
    display: block;
    width: 25px;
    height: 3px;
    background-color: var(--mainColor);
    margin: 5px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Hamburger animation */
.menu-mobile.active .icone span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.menu-mobile.active .icone span:nth-child(2) {
    opacity: 0;
}

.menu-mobile.active .icone span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

.side-bar {
    position: fixed;
    top: 55px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 55px);
    background: white;
    z-index: 1000;
    transition: left 0.3s ease-in-out;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow-y: auto;
}

.side-bar.show-menu {
    left: 0;
}

.side-bar ul {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.side-bar ul li a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    border-radius: 8px;
    background: #f8f9fa;
    color: #333;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.side-bar ul li a:hover {
    background: var(--mainColor);
    color: white;
    transform: translateX(5px);
    border-color: var(--mainColor);
}

.side-bar ul li a svg {
    width: 20px;
    height: 20px;
}

@media(max-width:768px){
    nav .icone span{
        display: block;
    }
    header nav{
        display: flex;
        width: 100%;
        align-items: center;
        padding: 0 15px;
    }
    header nav .icons ul{
        display: none;
    }
    header nav .link ul{
        display: none;
    }
    header nav .button {
        display: none;
    }
    .menu-mobile{
        display: flex;
        margin-left: auto;
    }
}
    
/* // ====pour nav bar de index.php-page===/// */
header nav .button{
    width: 40%;

}
header nav .button ul{
    display: flex;
    justify-content: end;
    gap: 3%;
}
header nav .button ul li a {
      display: block;
    background-color: var(--buttonColor);
    padding: 7PX 18PX;
    border-radius: 4px;
    font-size: 15px;
    color: white;
    font-weight: 500;
}
header nav .button ul li a.white-button{
    background-color: white;
    color: var(--buttonColor);
    border: var(--buttonColor) 1px solid;
}

/* ===== ENHANCED NAVBAR STYLES ===== */

/* Variables */
:root {
    --colorblue: #667eea;
    --mainColor: #0048A6;
    --buttonColor: #267CF4;
}

/* Logo improvements */
.logo a {
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--colorblue);
    display: none;
}

/* Navigation links with icons */
.link ul li a {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.link ul li a:hover {
    background: rgba(102, 126, 234, 0.1);
    color: var(--colorblue);
}

.link ul li a.active {
    background: var(--colorblue);
    color: white;
}

.link ul li a svg {
    width: 18px;
    height: 18px;
}

/* Auth buttons for guests */
.auth-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

.btn-login, .btn-register {
    padding: 8px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-login {
    color: var(--colorblue);
    border: 1px solid var(--colorblue);
}

.btn-login:hover {
    background: var(--colorblue);
    color: white;
}

.btn-register {
    background: var(--colorblue);
    color: white;
}

.btn-register:hover {
    background: #5a67d8;
}

/* Icons section improvements */
.icons ul li {
    position: relative;
}

.icons ul li a {
    position: relative;
    padding: 10px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.icons ul li a:hover {
    background: rgba(102, 126, 234, 0.1);
    color: var(--colorblue);
}

.icons ul li a.active {
    background: var(--colorblue);
    color: white;
}

/* Notification badge */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* Dropdown styles */
.dropdown {
    position: relative;
}

.dropdown:hover .new-publication-types,
.dropdown:hover .notifications-dropdown,
.dropdown:hover .profile-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* New publication dropdown */
.new-publication-types {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    padding: 10px;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.new-publication-types a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    color: #333;
    text-decoration: none;
    border-radius: 6px;
    transition: background 0.3s ease;
}

.new-publication-types a:hover {
    background: #f8f9fa;
    color: var(--colorblue);
}

/* Notifications dropdown */
.notifications-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    width: 350px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.notifications-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notifications-header h4 {
    margin: 0;
    color: #333;
}

.mark-all-read {
    color: var(--colorblue);
    text-decoration: none;
    font-size: 12px;
}

.notifications-list {
    max-height: 300px;
    overflow-y: auto;
}

.no-notifications {
    padding: 30px 20px;
    text-align: center;
    color: #666;
}

.notifications-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    text-align: center;
}

.notifications-footer a {
    color: var(--colorblue);
    text-decoration: none;
    font-size: 14px;
}

/* Profile dropdown */
.profile-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    min-width: 250px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.profile-info {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    gap: 15px;
    align-items: center;
}

.profile-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 18px;
}

.profile-details {
    flex: 1;
}

.profile-name {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.profile-email {
    display: block;
    font-size: 12px;
    color: #666;
}

.profile-menu {
    padding: 10px;
}

.profile-menu a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    color: #333;
    text-decoration: none;
    border-radius: 6px;
    transition: background 0.3s ease;
}

.profile-menu a:hover {
    background: #f8f9fa;
    color: var(--colorblue);
}

.profile-menu hr {
    margin: 10px 0;
    border: none;
    border-top: 1px solid #eee;
}

.logout-link {
    color: #e74c3c !important;
}

.logout-link:hover {
    background: #fdf2f2 !important;
    color: #c0392b !important;
}

/* Enhanced mobile sidebar */
.sidebar-header {
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin: -20px -20px 20px -20px;
}

.user-info {
    display: flex;
    gap: 15px;
    align-items: center;
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 18px;
}

.user-details {
    flex: 1;
}

.user-name {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
}

.user-email {
    display: block;
    font-size: 12px;
    opacity: 0.8;
}

.sidebar-menu {
    padding: 0;
    margin: 0;
    list-style: none;
}

.sidebar-menu li a {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    margin: 0 -20px;
}

.sidebar-menu li a:hover {
    background: #f8f9fa;
    color: var(--colorblue);
}

.sidebar-menu li a.active {
    background: var(--colorblue);
    color: white;
}

.sidebar-menu li a svg {
    width: 20px;
    height: 20px;
}

.sidebar-menu .divider {
    height: 1px;
    background: #eee;
    margin: 10px 0;
}

.publication-section {
    padding: 15px 20px 5px;
}

.section-title {
    font-size: 12px;
    font-weight: 600;
    color: #999;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Mobile notification badge */
.notification-badge.mobile {
    position: absolute;
    top: -5px;
    right: -5px;
}

/* Mobile responsive adjustments */
@media (max-width: 767px) {
    .logo-text {
        display: inline;
        font-size: 1.2rem;
    }

    .auth-buttons {
        flex-direction: column;
        gap: 5px;
    }

    .btn-login, .btn-register {
        padding: 6px 15px;
        font-size: 14px;
    }

    /* Hide desktop dropdowns on mobile */
    .new-publication-types,
    .notifications-dropdown,
    .profile-dropdown {
        display: none;
    }
}
