-- YADFYAD Database Setup
-- Create database and tables for the YADFYAD application

-- Create database
CREATE DATABASE IF NOT EXISTS yadfyad CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE yadfyad;

-- Table for associations
CREATE TABLE IF NOT EXISTS association (
    ID_ASSOCIATION INT AUTO_INCREMENT PRIMARY KEY,
    NOM_ASSOCIATION VARCHAR(255) NOT NULL,
    EMAIL VARCHAR(255) UNIQUE NOT NULL,
    MOT_DE_PASSE VARCHAR(255) NOT NULL,
    INFO TEXT,
    ADRESSE VARCHAR(500),
    NUMERO_TELEPHONE VARCHAR(20),
    DOMAIN<PERSON> VARCHAR(100),
    VERIFIE BOOLEAN DEFAULT FALSE,
    STATUT ENUM('EN_ATTENTE', 'VERIFIE', 'REJETE') DEFAULT 'EN_ATTENTE',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table for users (members)
CREATE TABLE IF NOT EXISTS utilisateur (
    ID_UTILISATEUR INT AUTO_INCREMENT PRIMARY KEY,
    NOM VARCHAR(100) NOT NULL,
    PRENOM VARCHAR(100) NOT NULL,
    EMAIL VARCHAR(255) UNIQUE NOT NULL,
    MOT_DE_PASSE VARCHAR(255) NOT NULL,
    DESCRIPTION TEXT,
    TYPE_UTILISATEUR TINYINT DEFAULT 0, -- 0: member, 1: admin
    ID_ASSOCIATION INT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (ID_ASSOCIATION) REFERENCES association(ID_ASSOCIATION) ON DELETE SET NULL
);

-- Table for publications
CREATE TABLE IF NOT EXISTS publication (
    ID_PUB INT AUTO_INCREMENT PRIMARY KEY,
    TITRE VARCHAR(255) NOT NULL,
    DISCRIPTION TEXT NOT NULL,
    LIEU_EVENEMENT_LACTIVITE VARCHAR(255),
    TYPE_PUB ENUM('problème', 'activité', 'expérience') NOT NULL,
    ID_UTILISATEUR INT NOT NULL,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (ID_UTILISATEUR) REFERENCES utilisateur(ID_UTILISATEUR) ON DELETE CASCADE
);

-- Table for media files
CREATE TABLE IF NOT EXISTS medias_url (
    ID_MEDIA INT AUTO_INCREMENT PRIMARY KEY,
    NOM_MEDIA VARCHAR(255) NOT NULL,
    ID_PUB INT NOT NULL,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ID_PUB) REFERENCES publication(ID_PUB) ON DELETE CASCADE
);

-- Table for likes
CREATE TABLE IF NOT EXISTS liker (
    ID_LIKE INT AUTO_INCREMENT PRIMARY KEY,
    ID_UTILISATEUR INT NOT NULL,
    ID_PUB INT NOT NULL,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ID_UTILISATEUR) REFERENCES utilisateur(ID_UTILISATEUR) ON DELETE CASCADE,
    FOREIGN KEY (ID_PUB) REFERENCES publication(ID_PUB) ON DELETE CASCADE,
    UNIQUE KEY unique_like (ID_UTILISATEUR, ID_PUB)
);

-- Table for comments
CREATE TABLE IF NOT EXISTS commentaire (
    ID_COMMENTAIRE INT AUTO_INCREMENT PRIMARY KEY,
    CONTENU TEXT NOT NULL,
    ID_UTILISATEUR INT NOT NULL,
    ID_PUB INT NOT NULL,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ID_UTILISATEUR) REFERENCES utilisateur(ID_UTILISATEUR) ON DELETE CASCADE,
    FOREIGN KEY (ID_PUB) REFERENCES publication(ID_PUB) ON DELETE CASCADE
);

-- Table for following relationships
CREATE TABLE IF NOT EXISTS suivre (
    ID_SUIVRE INT AUTO_INCREMENT PRIMARY KEY,
    ID_SUIVEUR INT NOT NULL,
    ID_SUIVI INT NOT NULL,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ID_SUIVEUR) REFERENCES utilisateur(ID_UTILISATEUR) ON DELETE CASCADE,
    FOREIGN KEY (ID_SUIVI) REFERENCES utilisateur(ID_UTILISATEUR) ON DELETE CASCADE,
    UNIQUE KEY unique_follow (ID_SUIVEUR, ID_SUIVI)
);

-- Table for chat messages
CREATE TABLE IF NOT EXISTS chater (
    ID_MESSAGE INT AUTO_INCREMENT PRIMARY KEY,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    message TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_id) REFERENCES utilisateur(ID_UTILISATEUR) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES utilisateur(ID_UTILISATEUR) ON DELETE CASCADE
);

-- Insert sample data for testing
INSERT INTO association (NOM_ASSOCIATION, EMAIL, MOT_DE_PASSE, INFO, ADRESSE, NUMERO_TELEPHONE, DOMAINE, VERIFIE, STATUT) VALUES
('Association Solidarité Maroc', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Aide alimentaire et soutien aux personnes défavorisées', 'Casablanca, Maroc', '+212600000001', 'Social', TRUE, 'VERIFIE'),
('Jeunesse et Avenir', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Éducation et accompagnement des jeunes', 'Rabat, Maroc', '+212600000002', 'Éducation', TRUE, 'VERIFIE'),
('Environnement Vert', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Protection de l\'environnement et sensibilisation', 'Marrakech, Maroc', '+212600000003', 'Environnement', FALSE, 'EN_ATTENTE');

INSERT INTO utilisateur (NOM, PRENOM, EMAIL, MOT_DE_PASSE, DESCRIPTION, TYPE_UTILISATEUR, ID_ASSOCIATION) VALUES
('Alami', 'Ahmed', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Coordinateur des activités sociales', 0, 1),
('Benali', 'Fatima', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Responsable des programmes éducatifs', 0, 2),
('Tazi', 'Youssef', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Militant écologiste', 0, 3),
('Admin', 'System', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrateur système', 1, NULL);

INSERT INTO publication (TITRE, DISCRIPTION, LIEU_EVENEMENT_LACTIVITE, TYPE_PUB, ID_UTILISATEUR) VALUES
('Besoin urgent de dons alimentaires', 'Nous recherchons des dons alimentaires pour les familles dans le besoin. Toute contribution sera la bienvenue.', 'Casablanca', 'problème', 1),
('Formation en informatique pour jeunes', 'Organisation d\'une formation gratuite en informatique pour les jeunes de 16 à 25 ans.', 'Rabat', 'activité', 2),
('Campagne de nettoyage réussie', 'Nous avons organisé une campagne de nettoyage qui a mobilisé plus de 100 volontaires.', 'Marrakech', 'expérience', 3);
