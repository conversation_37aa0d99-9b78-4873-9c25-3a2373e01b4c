# YADFYAD - Platform for Solidarity Associations

YADFYAD is a collaborative platform designed for solidarity associations in Morocco to connect, share their needs and problems, activities, and help each other.

## 🚀 Features

- **Association Profiles**: Create and manage association profiles
- **User Management**: Individual members can join and participate
- **Publications**: Share problems, activities, and experiences
- **Social Interactions**: Like, comment, and follow other associations
- **Chat System**: Direct messaging between users
- **Admin Panel**: Administrative interface for managing the platform

## 📋 Requirements

- PHP 8.0 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx) or PHP built-in server
- XAMPP (recommended for local development)

## 🛠️ Installation & Setup

### 1. Clone or Download the Project
```bash
git clone [repository-url]
cd YADFYAD-PFE
```

### 2. Database Setup
The app includes an automated database setup script:

```bash
php setup_database.php
```

This will:
- Create the `yadfyad` database
- Create all necessary tables
- Insert sample data for testing

### 3. Start the Application
Using PHP built-in server:
```bash
php -S localhost:8000
```

Or place the files in your XAMPP htdocs folder and access via:
```
http://localhost/YADFYAD-PFE
```

### 4. Test the Installation
Visit: `http://localhost:8000/test_app.php` to verify everything is working correctly.

## 🔐 Test Credentials

### Association Login
- **Email**: <EMAIL>
- **Password**: password

### User Login
- **Email**: <EMAIL>
- **Password**: password

### Admin Login
- **Email**: <EMAIL>
- **Password**: password

## 📁 Project Structure

```
YADFYAD-PFE/
├── index.php                 # Landing page
├── connexion.php             # Login page
├── inscription.php           # Association registration
├── inscription-mem.php       # User registration
├── actualite.php             # News feed
├── associations.php          # Browse associations
├── chat.php                  # Chat interface
├── profile.php               # User profiles
├── config.php                # Database configuration
├── assets/
│   ├── styles/
│   │   ├── style.css         # Main styles
│   │   └── landingpage.css   # Landing page styles
│   └── images/
│       └── logo.png          # App logo
├── sections/
│   ├── navbar.php            # Navigation bar
│   └── landing-navbar.php    # Landing page navigation
├── requets/
│   ├── like.php              # Like functionality
│   └── suivre.php            # Follow functionality
├── publication/
│   ├── activite.php          # Activity publications
│   ├── experience.php        # Experience publications
│   └── probleme.php          # Problem publications
├── admin/                    # Admin panel
├── script.js                 # Main JavaScript
├── requets.js                # AJAX requests
└── database_setup.sql        # Database schema
```

## 🎯 How to Use

### For Associations
1. **Register**: Go to inscription.php to create an association account
2. **Login**: Use connexion.php to access your account
3. **Create Profile**: Complete your association information
4. **Publish**: Share problems, activities, or experiences
5. **Network**: Follow other associations and interact with their content

### For Individual Users
1. **Register**: Go to inscription-mem.php to create a user account
2. **Join Association**: Link your account to an association
3. **Participate**: Like, comment, and share content
4. **Chat**: Communicate with other users

### For Administrators
1. **Access Admin Panel**: Login with admin credentials
2. **Verify Associations**: Approve new association registrations
3. **Manage Content**: Monitor and moderate platform content
4. **View Analytics**: Track platform usage and growth

## 🔧 Configuration

### Database Configuration
Edit `config.php` to modify database settings:
```php
$host = 'localhost';
$dbname = 'yadfyad';
$username = 'root';
$password = '';
```

### Environment Setup
- Ensure PHP error reporting is enabled during development
- Configure proper file permissions for uploads
- Set up SSL for production environments

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Verify MySQL is running
   - Check database credentials in config.php
   - Run setup_database.php again

2. **CSS/JS Not Loading**
   - Check file paths in HTML
   - Verify web server configuration
   - Clear browser cache

3. **Login Issues**
   - Use provided test credentials
   - Check if database tables exist
   - Verify password hashing is working

### Getting Help
- Check the test_app.php page for system status
- Review PHP error logs
- Ensure all required files are present

## 🚀 Going Live

For production deployment:
1. Use a proper web server (Apache/Nginx)
2. Configure SSL certificates
3. Update database credentials
4. Enable proper error logging
5. Set up regular backups
6. Configure email settings for notifications

## 📝 License

This project is developed for educational purposes as part of a PFE (Projet de Fin d'Études).

---

**Status**: ✅ Ready to use! The app is fully functional with all core features working.
