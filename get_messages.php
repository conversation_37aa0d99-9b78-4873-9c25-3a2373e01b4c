<?php
/**
 * Récupération des messages - Compatibilité avec l'ancien système
 * Redirige vers le nouveau backend
 */

session_start();
require_once 'config.php';

// Configuration des headers
header('Content-Type: application/json');

// Vérification de l'authentification
if (!isset($_SESSION['email'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Non authentifié']);
    exit;
}

// Récupération du receiver_id
$receiverId = $_GET['receiver_id'] ?? null;

if (!$receiverId) {
    http_response_code(400);
    echo json_encode(['error' => 'receiver_id requis']);
    exit;
}

// Récupération de l'utilisateur actuel
try {
    $stmt = $pdo->prepare("SELECT ID_UTILISATEUR FROM utilisateur WHERE EMAIL = :email");
    $stmt->execute([':email' => $_SESSION['email']]);
    $currentUser = $stmt->fetch();
    
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['error' => 'Utilisateur non trouvé']);
        exit;
    }
    
    $currentUserId = $currentUser['ID_UTILISATEUR'];
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur de base de données']);
    exit;
}

// Validation du receiver_id
$receiverId = (int)$receiverId;
if ($receiverId <= 0) {
    http_response_code(400);
    echo json_encode(['error' => 'receiver_id invalide']);
    exit;
}

// Récupération des messages
try {
    $sql = "
        SELECT 
            m.ID_MESSAGE,
            m.sender_id,
            m.receiver_id,
            m.message,
            m.message_type,
            m.is_read,
            m.created_at,
            u.NOM,
            u.PRENOM
        FROM chater m
        JOIN utilisateur u ON m.sender_id = u.ID_UTILISATEUR
        WHERE 
            (m.sender_id = :current_user AND m.receiver_id = :receiver_id) OR
            (m.sender_id = :receiver_id2 AND m.receiver_id = :current_user2)
        ORDER BY m.created_at ASC
        LIMIT 100
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':current_user' => $currentUserId,
        ':current_user2' => $currentUserId,
        ':receiver_id' => $receiverId,
        ':receiver_id2' => $receiverId
    ]);
    
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Formatage des messages pour compatibilité
    $formattedMessages = array_map(function($msg) use ($currentUserId) {
        return [
            'id' => $msg['ID_MESSAGE'],
            'message' => $msg['message'],
            'sender_id' => $msg['sender_id'],
            'receiver_id' => $msg['receiver_id'],
            'sender_name' => $msg['PRENOM'] . ' ' . $msg['NOM'],
            'is_sent' => $msg['sender_id'] == $currentUserId,
            'is_read' => (bool)$msg['is_read'],
            'time' => date('H:i', strtotime($msg['created_at'])),
            'date' => date('Y-m-d', strtotime($msg['created_at'])),
            'created_at' => $msg['created_at'],
            'type' => $msg['message_type']
        ];
    }, $messages);
    
    // Marquer les messages reçus comme lus
    if (!empty($messages)) {
        $markReadSql = "
            UPDATE chater 
            SET is_read = TRUE 
            WHERE sender_id = :sender_id AND receiver_id = :receiver_id AND is_read = FALSE
        ";
        $markReadStmt = $pdo->prepare($markReadSql);
        $markReadStmt->execute([
            ':sender_id' => $receiverId,
            ':receiver_id' => $currentUserId
        ]);
    }
    
    // Retourner les messages (format compatible avec l'ancien système)
    echo json_encode($formattedMessages);
    
} catch (PDOException $e) {
    // Log de l'erreur
    error_log("Erreur récupération messages: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode(['error' => 'Erreur lors de la récupération des messages']);
}
?>
