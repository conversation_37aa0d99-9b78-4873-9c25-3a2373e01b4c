<?php
/**
 * Test script to verify the YADFYAD app is working correctly
 */

echo "<h1>YADFYAD App Test Results</h1>";

// Test 1: Database Connection
echo "<h2>1. Database Connection Test</h2>";
try {
    require_once 'config.php';
    echo "✅ Database connection successful<br>";
    
    // Test tables exist
    $tables = ['association', 'utilisateur', 'publication', 'liker', 'suivre'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        echo "✅ Table '$table' exists with $count records<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test 2: Sample Data
echo "<h2>2. Sample Data Test</h2>";
try {
    $stmt = $pdo->query("SELECT * FROM association LIMIT 3");
    $associations = $stmt->fetchAll();
    echo "✅ Found " . count($associations) . " sample associations:<br>";
    foreach ($associations as $assoc) {
        echo "- " . $assoc['NOM_ASSOCIATION'] . " (" . $assoc['EMAIL'] . ")<br>";
    }
    
    $stmt = $pdo->query("SELECT * FROM utilisateur LIMIT 3");
    $users = $stmt->fetchAll();
    echo "✅ Found " . count($users) . " sample users:<br>";
    foreach ($users as $user) {
        echo "- " . $user['PRENOM'] . " " . $user['NOM'] . " (" . $user['EMAIL'] . ")<br>";
    }
} catch (Exception $e) {
    echo "❌ Sample data error: " . $e->getMessage() . "<br>";
}

// Test 3: Password Verification
echo "<h2>3. Password Test</h2>";
try {
    $testPassword = 'password';
    $stmt = $pdo->prepare("SELECT MOT_DE_PASSE FROM association WHERE EMAIL = '<EMAIL>'");
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result && password_verify($testPassword, $result['MOT_DE_PASSE'])) {
        echo "✅ Password verification working correctly<br>";
    } else {
        echo "❌ Password verification failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Password test error: " . $e->getMessage() . "<br>";
}

// Test 4: File Structure
echo "<h2>4. File Structure Test</h2>";
$requiredFiles = [
    'index.php' => 'Landing page',
    'connexion.php' => 'Login page',
    'inscription.php' => 'Registration page',
    'actualite.php' => 'News feed page',
    'assets/styles/style.css' => 'Main CSS',
    'assets/styles/landingpage.css' => 'Landing page CSS',
    'assets/images/logo.png' => 'Logo image',
    'script.js' => 'Main JavaScript',
    'requets.js' => 'AJAX requests JavaScript'
];

foreach ($requiredFiles as $file => $description) {
    if (file_exists($file)) {
        echo "✅ $description ($file)<br>";
    } else {
        echo "❌ Missing: $description ($file)<br>";
    }
}

echo "<h2>5. Test Credentials</h2>";
echo "<p>You can test the app with these credentials:</p>";
echo "<strong>Association Login:</strong><br>";
echo "Email: <EMAIL><br>";
echo "Password: password<br><br>";

echo "<strong>User Login:</strong><br>";
echo "Email: <EMAIL><br>";
echo "Password: password<br><br>";

echo "<strong>Admin Login:</strong><br>";
echo "Email: <EMAIL><br>";
echo "Password: password<br><br>";

echo "<h2>6. Quick Links</h2>";
echo "<a href='index.php'>🏠 Home Page</a> | ";
echo "<a href='connexion.php'>🔐 Login</a> | ";
echo "<a href='inscription.php'>📝 Register Association</a> | ";
echo "<a href='inscription-mem.php'>👤 Register User</a>";

echo "<br><br><strong>App Status: ";
if (isset($pdo)) {
    echo "🟢 READY TO USE!</strong>";
} else {
    echo "🔴 NEEDS SETUP</strong>";
}
?>
