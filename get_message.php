<?php
session_start();
header('Content-Type: application/json');

$pdo = new PDO('mysql:host=localhost;dbname=yadfyad;charset=utf8', 'root', '', [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
]);

if (!isset($_SESSION['user_id'])) {
    echo json_encode([]);
    exit;
}

$user_id = $_SESSION['user_id'];
$receiver_id = $_GET['receiver_id'] ?? null;

if (!$receiver_id) {
    echo json_encode([]);
    exit;
}

// On récupère les messages entre user_id et receiver_id (dans les deux sens)
$stmt = $pdo->prepare("
    SELECT sender_id, message, created_at 
    FROM chater 
    WHERE (sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)
    ORDER BY created_at ASC
");

$stmt->execute([$user_id, $receiver_id, $receiver_id, $user_id]);

$messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo json_encode($messages);
